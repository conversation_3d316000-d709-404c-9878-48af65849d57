//+------------------------------------------------------------------+
//|                                              PanelManager.mqh |
//|                                  面板管理器                      |
//|                                  负责UI显示和更新               |
//+------------------------------------------------------------------+
#property copyright "Professional Volatility Dashboard"
#property link      ""

#include "Defines.mqh"

//+------------------------------------------------------------------+
//| 面板管理器类                                                     |
//+------------------------------------------------------------------+
class CPanelManager
{
private:
    SPanelConfig      m_config;
    bool              m_initialized;
    
    // 对象名称
    string            m_panelName;
    string            m_macroSectionName;
    string            m_mesoSectionName;
    string            m_microSectionName;
    
    // 创建面板对象
    bool              CreatePanel();
    bool              CreateSection(string sectionName, int yOffset, string title);
    bool              CreateText(string textName, int x, int y, string text, color textColor = clrNONE);
    
    // 清理对象
    void              CleanupObjects();
    
public:
    CPanelManager();
    ~CPanelManager();
    
    // 初始化
    bool              Initialize(const SPanelConfig &config);
    
    // 更新数据
    bool              UpdateMacroData(const SMacroData &data);
    bool              UpdateMesoData(const SMesoData &data);
    bool              UpdateMicroData(const SMicroData &data);
    
    // 刷新面板
    void              RefreshPanel();
    
    // 事件处理
    void              OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam);
    
    // 清理
    void              Cleanup();
};

//+------------------------------------------------------------------+
//| 构造函数                                                         |
//+------------------------------------------------------------------+
CPanelManager::CPanelManager()
{
    m_initialized = false;
}

//+------------------------------------------------------------------+
//| 析构函数                                                         |
//+------------------------------------------------------------------+
CPanelManager::~CPanelManager()
{
    Cleanup();
}

//+------------------------------------------------------------------+
//| 初始化                                                           |
//+------------------------------------------------------------------+
bool CPanelManager::Initialize(const SPanelConfig &config)
{
    m_config = config;
    
    // 生成唯一对象名称
    m_panelName = GENERATE_OBJ_NAME(OBJ_PREFIX_PANEL, "Main");
    m_macroSectionName = GENERATE_OBJ_NAME(OBJ_PREFIX_SECTION, "Macro");
    m_mesoSectionName = GENERATE_OBJ_NAME(OBJ_PREFIX_SECTION, "Meso");
    m_microSectionName = GENERATE_OBJ_NAME(OBJ_PREFIX_SECTION, "Micro");
    
    // 创建面板
    if(!CreatePanel())
    {
        Print("面板管理器：创建面板失败");
        return false;
    }
    
    m_initialized = true;
    return true;
}

//+------------------------------------------------------------------+
//| 创建面板                                                         |
//+------------------------------------------------------------------+
bool CPanelManager::CreatePanel()
{
    // 创建主面板
    if(!ObjectCreate(0, m_panelName, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        Print("面板管理器：无法创建主面板");
        return false;
    }
    
    ObjectSetInteger(0, m_panelName, OBJPROP_CORNER, m_config.corner);
    ObjectSetInteger(0, m_panelName, OBJPROP_XDISTANCE, m_config.xOffset);
    ObjectSetInteger(0, m_panelName, OBJPROP_YDISTANCE, m_config.yOffset);
    ObjectSetInteger(0, m_panelName, OBJPROP_XSIZE, PANEL_WIDTH);
    ObjectSetInteger(0, m_panelName, OBJPROP_YSIZE, PANEL_HEIGHT);
    ObjectSetInteger(0, m_panelName, OBJPROP_BGCOLOR, m_config.panelColor);
    ObjectSetInteger(0, m_panelName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, m_panelName, OBJPROP_COLOR, COLOR_BORDER);
    ObjectSetInteger(0, m_panelName, OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, m_panelName, OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, m_panelName, OBJPROP_BACK, false);
    ObjectSetInteger(0, m_panelName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, m_panelName, OBJPROP_SELECTED, false);
    ObjectSetInteger(0, m_panelName, OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, m_panelName, OBJPROP_ZORDER, 0);
    
    // 创建各个区域
    CreateSection(m_macroSectionName, 5, "宏观分析");
    CreateSection(m_mesoSectionName, 65, "中观分析");
    CreateSection(m_microSectionName, 125, "微观分析");
    
    return true;
}

//+------------------------------------------------------------------+
//| 创建区域                                                         |
//+------------------------------------------------------------------+
bool CPanelManager::CreateSection(string sectionName, int yOffset, string title)
{
    // 创建标题
    string titleName = sectionName + "_Title";
    CreateText(titleName, 10, yOffset, title, COLOR_TITLE);
    
    return true;
}

//+------------------------------------------------------------------+
//| 创建文本                                                         |
//+------------------------------------------------------------------+
bool CPanelManager::CreateText(string textName, int x, int y, string text, color textColor = clrNONE)
{
    if(!ObjectCreate(0, textName, OBJ_LABEL, 0, 0, 0))
        return false;
    
    ObjectSetInteger(0, textName, OBJPROP_CORNER, m_config.corner);
    ObjectSetInteger(0, textName, OBJPROP_XDISTANCE, m_config.xOffset + x);
    ObjectSetInteger(0, textName, OBJPROP_YDISTANCE, m_config.yOffset + y);
    ObjectSetString(0, textName, OBJPROP_TEXT, text);
    ObjectSetString(0, textName, OBJPROP_FONT, FONT_NAME);
    ObjectSetInteger(0, textName, OBJPROP_FONTSIZE, m_config.fontSize);
    ObjectSetInteger(0, textName, OBJPROP_COLOR, textColor == clrNONE ? m_config.textColor : textColor);
    ObjectSetInteger(0, textName, OBJPROP_BACK, false);
    ObjectSetInteger(0, textName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, textName, OBJPROP_SELECTED, false);
    ObjectSetInteger(0, textName, OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, textName, OBJPROP_ZORDER, 1);
    
    return true;
}

//+------------------------------------------------------------------+
//| 更新宏观数据                                                     |
//+------------------------------------------------------------------+
bool CPanelManager::UpdateMacroData(const SMacroData &data)
{
    if(!m_initialized || !data.isValid)
        return false;
    
    string slowADRText = m_macroSectionName + "_SlowADR";
    string fastADRText = m_macroSectionName + "_FastADR";
    string todayRangeText = m_macroSectionName + "_TodayRange";
    string completionText = m_macroSectionName + "_Completion";
    
    CreateText(slowADRText, 10, 20, "慢速ADR: " + DoubleToString(data.slowADR, 1), COLOR_TEXT);
    CreateText(fastADRText, 10, 35, "快速ADR: " + DoubleToString(data.fastADR, 1), COLOR_TEXT);
    CreateText(todayRangeText, 10, 50, "今日波幅: " + DoubleToString(data.todayRange, 1), COLOR_VALUE);
    CreateText(completionText, 120, 50, "完成度: " + DoubleToString(data.completionPercent, 1) + "%", COLOR_VALUE);
    
    return true;
}

//+------------------------------------------------------------------+
//| 更新中观数据                                                     |
//+------------------------------------------------------------------+
bool CPanelManager::UpdateMesoData(const SMesoData &data)
{
    if(!m_initialized || !data.isValid)
        return false;
    
    string currentBlockText = m_mesoSectionName + "_CurrentBlock";
    string previousBlockText = m_mesoSectionName + "_PreviousBlock";
    
    CreateText(currentBlockText, 10, 80, "当前时段: " + DoubleToString(data.currentBlockVolatility, 1), COLOR_TEXT);
    CreateText(previousBlockText, 10, 95, "前序时段: " + DoubleToString(data.previousBlockVolatility, 1), COLOR_TEXT);
    
    return true;
}

//+------------------------------------------------------------------+
//| 更新微观数据                                                     |
//+------------------------------------------------------------------+
bool CPanelManager::UpdateMicroData(const SMicroData &data)
{
    if(!m_initialized || !data.isValid)
        return false;
    
    string fastRangeText = m_microSectionName + "_FastRange";
    string slowRangeText = m_microSectionName + "_SlowRange";
    string ratioText = m_microSectionName + "_Ratio";
    string statusText = m_microSectionName + "_Status";
    
    CreateText(fastRangeText, 10, 140, "快速波幅: " + DoubleToString(data.fastAvgRange, 1), COLOR_TEXT);
    CreateText(slowRangeText, 10, 155, "慢速波幅: " + DoubleToString(data.slowAvgRange, 1), COLOR_TEXT);
    CreateText(ratioText, 120, 140, "比率: " + DoubleToString(data.volatilityRatio, 2), COLOR_VALUE);
    
    string statusString = "";
    switch(data.status)
    {
        case VOLATILITY_HEATING_UP:
            statusString = "升温中";
            break;
        case VOLATILITY_COOLING_DOWN:
            statusString = "降温中";
            break;
        case VOLATILITY_CONSOLIDATING:
            statusString = "盘整中";
            break;
    }
    
    CreateText(statusText, 120, 155, "状态: " + statusString, data.statusColor);
    
    return true;
}

//+------------------------------------------------------------------+
//| 刷新面板                                                         |
//+------------------------------------------------------------------+
void CPanelManager::RefreshPanel()
{
    if(m_initialized)
    {
        ChartRedraw();
    }
}

//+------------------------------------------------------------------+
//| 事件处理                                                         |
//+------------------------------------------------------------------+
void CPanelManager::OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    // 处理图表事件
}

//+------------------------------------------------------------------+
//| 清理对象                                                         |
//+------------------------------------------------------------------+
void CPanelManager::CleanupObjects()
{
    // 删除所有相关对象
    int totalObjects = ObjectsTotal(0, -1, -1);
    for(int i = totalObjects - 1; i >= 0; i--)
    {
        string objName = ObjectName(0, i, -1, -1);
        if(StringFind(objName, m_config.indicatorName) >= 0)
        {
            ObjectDelete(0, objName);
        }
    }
}

//+------------------------------------------------------------------+
//| 清理                                                             |
//+------------------------------------------------------------------+
void CPanelManager::Cleanup()
{
    if(m_initialized)
    {
        CleanupObjects();
        m_initialized = false;
    }
}