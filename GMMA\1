MQL5 EA开发需求：基于顾比复合移动平均线（GMMA）的全自动趋势跟踪系统
项目标题： GMMA_Trend_Follower_EA

项目目标： 开发一个全自动的MQL5 EA，该EA严格基于戴若·顾比（<PERSON> Guppy）的复合移动平均线（GMMA）理论进行交易。EA需具备完整的市场状态分析、进出场逻辑、动态止损、以及高级资金管理功能（包括基于交易表现的风险调整，即“赢冲输缩”）。

第一部分：核心指标与输入参数 (Inputs)
EA需要提供以下可配置的输入参数，以便于用户进行优化和自定义。

EA身份标识

MagicNumber (long): EA的唯一魔法数字。

EaComment (string): 交易订单的注释。

GMMA指标参数

GmmaShortPeriods (string): 短期均线组周期，用逗号分隔，例如 "3,5,8,10,12,15"。

GmmaLongPeriods (string): 长期均线组周期，用逗号分隔，例如 "30,35,40,45,50,60"。

GmmaMethod (ENUM_MA_METHOD): 移动平均线的计算方法（默认为 MODE_EMA）。

GmmaAppliedPrice (ENUM_APPLIED_PRICE): 应用价格（默认为 PRICE_CLOSE）。

资金管理参数

RiskPerTradePercent (double): 每单初始风险占账户净值的百分比（例如，输入2.0代表2%）。

UseWinningStreak (bool): 是否启用“赢冲”逻辑。

WinningStreakThreshold (int): 连续盈利多少笔后，触发“赢冲”（例如，3）。

WinningStreakRiskMultiplier (double): “赢冲”时，风险百分比的乘数（例如，1.5，即将风险从2%提高到3%）。

UseLosingStreak (bool): 是否启用“输缩”逻辑。

LosingStreakThreshold (int): 连续亏损多少笔后，触发“输缩”（例如，2）。

LosingStreakRiskMultiplier (double): “输缩”时，风险百分比的乘数（例如，0.5，即将风险从2%降低到1%）。

交易逻辑参数

AtrPeriod (int): 用于计算止损距离的ATR指标周期（例如，14）。

AtrMultiplierForSL (double): ATR值的乘数，用于设定止损宽度（例如，1.5）。

MaxSpreadPoints (int): 允许开仓的最大点差。

第二部分：核心逻辑函数（模块化结构）
请将EA的逻辑划分为清晰的函数，以便于维护和理解。

GetMarketState() 函数:

目的： 分析长期均线组（Long Group）的状态，判断当前市场趋势。

逻辑：

计算长期组内所有均线之间的平均分离度（宽度）。

计算长期组的整体方向（斜率）。

返回一个枚举类型 (enum):

TREND_BULL：长期组稳定向上发散（斜率>0，分离度在扩大或保持稳定）。

TREND_BEAR：长期组稳定向下发散（斜率<0，分离度在扩大或保持稳定）。

TREND_RANGE：长期组横向移动，且组内均线相互缠绕、收敛（斜率接近0，分离度很小或在缩小）。

CheckEntrySignal() 函数:

目的： 在确定市场为 TREND_BULL 后，寻找精确的做多入场点。

逻辑（返回 bool 值）：

检查短期均线组（Short Group）是否已回落至长期均线组附近。

检查价格是否触及或短暂进入长期组，但未有效跌破。

确认短期组已停止下跌，并重新出现向上的迹象（例如，短期组的最低周期均线重新上穿较高周期均线）。

（可选增强） 确认回调期间成交量是萎缩的。

满足上述条件则返回 true。

CalculatePositionSize() 函数:

目的： 根据当前风险设置和止损距离，计算合适的交易手数。

逻辑：

获取当前账户净值 (AccountEquity())。

获取当前由 ManageTradeStreak() 函数调整后的实际风险百分比。

计算止损价格（详见出场逻辑）。

计算每手的风险金额。

公式： 手数 = (账户净值 * 风险百分比 / 100) / ((入场价 - 止损价) * 每手价值)。

需要进行手数的标准化（符合最小步长、不超过最大限制）。

ManageTradeStreak() 函数:

目的： 实现“赢冲输缩”的动态风险调整。

逻辑：

EA需内部追踪基于 MagicNumber 的连续盈利和亏损次数。

赢冲： 如果 UseWinningStreak 为 true，且连续盈利次数 >= WinningStreakThreshold，则返回 RiskPerTradePercent * WinningStreakRiskMultiplier。

输缩： 如果 UseLosingStreak 为 true，且连续亏损次数 >= LosingStreakThreshold，则返回 RiskPerTradePercent * LosingStreakRiskMultiplier。

常规： 其他情况下，返回 RiskPerTradePercent。

任何一次反向结果（盈利中断亏损，或亏损中断盈利）都会重置计数器。

第三部分：交易执行逻辑 (OnTick / OnNewBar)
建议使用 OnNewBar (在新K线生成时执行) 的模式，以保证信号稳定。

A. 做多进场逻辑 (Long Entry):

前提检查：

当前没有持仓 (PositionsTotal() == 0 for this MagicNumber)。

GetMarketState() 返回 TREND_BULL。

当前点差小于 MaxSpreadPoints。

信号确认：

调用 CheckEntrySignal()，如果返回 true。

执行交易：

计算止损价格 (SL)：长期组最下方均线值 - (ATR值 * AtrMultiplierForSL)。

调用 CalculatePositionSize() 计算手数。

执行 OrderSend 开立多单，不设置固定止盈价 (TP)，但必须设置计算好的止损价 (SL)。

B. 出场逻辑 (Position Exit):

EA需要实时监控持有的多单，并在满足以下任一条件时立即平仓：

止损触发： 价格触及设定的SL。

趋势衰竭信号：

GetMarketState() 返回 TREND_RANGE。这表示长期投资者开始犹豫，上涨趋势可能结束。

明确反转信号：

短期均线组整体 向下穿越 长期均线组。这是一个强烈的离场信号。

注意： EA不执行做空交易，仅在熊市和震荡市保持空仓观望。

第四部分：EA界面与显示
在图表左上角使用 Comment() 或对象标签显示EA的关键状态信息，包括：

EA名称和版本

当前市场状态 (BULL / BEAR / RANGE)

当前风险设置 (例如 "Risk: 2.0%, Mode: Normal")

连赢/连亏次数 (例如 "Streak: +2 Wins")

持仓状态（手数、盈亏等）

在图表上绘制全部12条GMMA均线，短期组和长期组使用不同颜色（例如，短期为蓝色系，长期为红色系）。

第五部分：架构与健壮性要求
代码结构清晰： 严格遵守上述函数模块化建议，添加充足的注释。

错误处理： 对交易执行失败（如OrderSend返回false）进行适当的日志记录和处理。

性能优化： 避免在 OnTick() 中进行不必要的重复计算。指标值在每根新K线开始时计算一次即可。

回测兼容性： 确保代码能够在MT5的策略测试器中正常运行。

请严格按照以上逻辑和架构进行开发，以确保EA的稳定性和交易逻辑的准确性。