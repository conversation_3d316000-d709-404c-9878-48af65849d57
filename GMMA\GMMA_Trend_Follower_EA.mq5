//+------------------------------------------------------------------+
//|                                      GMMA_Trend_Follower_EA.mq5 |
//|                                  基于顾比复合移动平均线的全自动EA |
//|                                                   Version 1.0.0 |
//+------------------------------------------------------------------+
#property copyright "GMMA Trend Follower EA"
#property link      ""
#property version   "1.00"
#property description "基于Daryl Guppy的复合移动平均线(GMMA)理论的全自动趋势跟踪EA"

//--- 包含必要的库文件
#include <Trade\Trade.mqh>
#include <Controls\Dialog.mqh>
#include <Controls\Button.mqh>
#include <Controls\Label.mqh>
#include "Include/GMMA_Indicator.mqh"
#include "Include/Signal_Manager.mqh"
#include "Include/Risk_Manager.mqh"
#include "Include/Trade_Manager.mqh"
#include "Include/UI_Panel.mqh"

//+------------------------------------------------------------------+
//| 输入参数                                                          |
//+------------------------------------------------------------------+
//--- EA身份标识
input group "=== EA身份标识 ==="
input long    MagicNumber = 20241001;                    // EA魔法数字
input string  EaComment = "GMMA_EA";                     // 交易注释

//--- GMMA指标参数
input group "=== GMMA指标参数 ==="
input string  GmmaShortPeriods = "3,5,8,10,12,15";      // 短期均线组周期
input string  GmmaLongPeriods = "30,35,40,45,50,60";    // 长期均线组周期
input ENUM_MA_METHOD GmmaMethod = MODE_EMA;              // 移动平均线计算方法
input ENUM_APPLIED_PRICE GmmaAppliedPrice = PRICE_CLOSE; // 应用价格

//--- 资金管理参数
input group "=== 资金管理参数 ==="
input double  RiskPerTradePercent = 2.0;                // 每单初始风险百分比
input bool    UseWinningStreak = true;                  // 启用赢冲逻辑
input int     WinningStreakThreshold = 3;               // 连续盈利触发阈值
input double  WinningStreakRiskMultiplier = 1.5;       // 赢冲风险乘数
input bool    UseLosingStreak = true;                   // 启用输缩逻辑
input int     LosingStreakThreshold = 2;                // 连续亏损触发阈值
input double  LosingStreakRiskMultiplier = 0.5;        // 输缩风险乘数

//--- 交易逻辑参数
input group "=== 交易逻辑参数 ==="
input int     AtrPeriod = 14;                           // ATR指标周期
input double  AtrMultiplierForSL = 1.5;                // ATR止损乘数
input int     MaxSpreadPoints = 30;                     // 最大允许点差

//--- 界面显示参数
input group "=== 界面显示参数 ==="
input bool    ShowPanel = true;                         // 显示控制面板
input bool    ShowGmmaLines = true;                     // 显示GMMA线条
input color   ShortGroupColor = clrDodgerBlue;          // 短期组颜色
input color   LongGroupColor = clrCrimson;              // 长期组颜色

//+------------------------------------------------------------------+
//| 全局变量                                                          |
//+------------------------------------------------------------------+
CTrade         trade;                    // 交易对象
CGMMA_Indicator gmma_indicator;          // GMMA指标对象
CSignal_Manager signal_manager;          // 信号管理器
CRisk_Manager   risk_manager;           // 风险管理器
CTrade_Manager  trade_manager;          // 交易管理器
CUI_Panel      ui_panel;               // 界面面板

//--- 市场状态枚举
enum ENUM_MARKET_STATE
{
   TREND_BULL,    // 牛市趋势
   TREND_BEAR,    // 熊市趋势
   TREND_RANGE    // 震荡市场
};

//--- 全局状态变量
ENUM_MARKET_STATE current_market_state = TREND_RANGE;
datetime last_bar_time = 0;
bool is_new_bar = false;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== GMMA Trend Follower EA 初始化开始 ===");
   
   //--- 设置交易对象
   //--- 设置交易对象
   trade.SetExpertMagicNumber(MagicNumber);
   trade.SetMarginMode();
   trade.SetTypeFillingBySymbol(Symbol());
   
   //--- 初始化各个模块
   if(!InitializeModules())
   {
      Print("模块初始化失败");
      return INIT_FAILED;
   }
   
   //--- 设置风险管理器的魔法数字
   risk_manager.SetMagicNumber(MagicNumber);
   
   //--- 创建界面面板
   if(ShowPanel && !ui_panel.Create())
   {
      Print("界面面板创建失败");
      return INIT_FAILED;
   }
   
   //--- 绘制GMMA线条
   if(ShowGmmaLines)
   {
      DrawGmmaLines();
   }
   
   Print("=== GMMA Trend Follower EA 初始化完成 ===");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("=== GMMA Trend Follower EA 反初始化 ===");
   
   //--- 清理界面
   ui_panel.Destroy();
   
   //--- 删除图表对象
   ObjectsDeleteAll(0, "GMMA_");
   
   //--- 清理模块
   CleanupModules();
   
   ChartRedraw();
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   //--- 检查是否有新K线
   CheckNewBar();
   
   if(!is_new_bar)
      return;
   
   //--- 更新GMMA指标
   if(!gmma_indicator.Update())
   {
      Print("GMMA指标更新失败");
      return;
   }
   
   //--- 获取市场状态
   current_market_state = GetMarketState();
   
   //--- 处理交易逻辑
   ProcessTradingLogic();
   
   //--- 更新界面显示
   if(ShowPanel)
   {
      UpdatePanel();
   }
   
   //--- 更新GMMA线条显示
   if(ShowGmmaLines)
   {
      UpdateGmmaLines();
   }
}

//+------------------------------------------------------------------+
//| 检查新K线                                                         |
//+------------------------------------------------------------------+
void CheckNewBar()
{
   datetime current_time = iTime(Symbol(), PERIOD_CURRENT, 0);
   is_new_bar = (current_time != last_bar_time);
   
   if(is_new_bar)
   {
      last_bar_time = current_time;
   }
}

//+------------------------------------------------------------------+
//| 初始化所有模块                                                    |
//+------------------------------------------------------------------+
bool InitializeModules()
{
   //--- 初始化GMMA指标
   if(!gmma_indicator.Initialize(GmmaShortPeriods, GmmaLongPeriods, GmmaMethod, GmmaAppliedPrice))
   {
      Print("GMMA指标初始化失败");
      return false;
   }
   
   //--- 初始化信号管理器
   if(!signal_manager.Initialize())
   {
      Print("信号管理器初始化失败");
      return false;
   }
   
   //--- 初始化风险管理器
   if(!risk_manager.Initialize(RiskPerTradePercent, UseWinningStreak, WinningStreakThreshold, 
                              WinningStreakRiskMultiplier, UseLosingStreak, LosingStreakThreshold, 
                              LosingStreakRiskMultiplier))
   {
      Print("风险管理器初始化失败");
      return false;
   }
   
   //--- 初始化交易管理器
   if(!trade_manager.Initialize(&trade, MagicNumber, AtrPeriod, AtrMultiplierForSL, MaxSpreadPoints))
   {
      Print("交易管理器初始化失败");
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 清理所有模块                                                      |
//+------------------------------------------------------------------+
void CleanupModules()
{
   gmma_indicator.Cleanup();
   signal_manager.Cleanup();
   risk_manager.Cleanup();
   trade_manager.Cleanup();
}

//+------------------------------------------------------------------+
//| 获取市场状态                                                      |
//+------------------------------------------------------------------+
ENUM_MARKET_STATE GetMarketState()
{
   //--- 获取长期组均线数据
   double long_group_values[];
   if(!gmma_indicator.GetLongGroupValues(long_group_values))
   {
      return TREND_RANGE;
   }
   
   //--- 计算长期组的平均分离度
   double separation = CalculateGroupSeparation(long_group_values);
   
   //--- 计算长期组的整体斜率
   double slope = CalculateGroupSlope(long_group_values);
   
   //--- 判断市场状态
   if(slope > 0 && separation > 0.0001) // 向上发散
   {
      return TREND_BULL;
   }
   else if(slope < 0 && separation > 0.0001) // 向下发散
   {
      return TREND_BEAR;
   }
   else // 横向或收敛
   {
      return TREND_RANGE;
   }
}

//+------------------------------------------------------------------+
//| 计算均线组分离度                                                  |
//+------------------------------------------------------------------+
double CalculateGroupSeparation(const double &values[])
{
   if(ArraySize(values) < 2)
      return 0.0;
   
   double max_val = values[0];
   double min_val = values[0];
   
   for(int i = 1; i < ArraySize(values); i++)
   {
      if(values[i] > max_val) max_val = values[i];
      if(values[i] < min_val) min_val = values[i];
   }
   
   return (max_val - min_val) / min_val; // 相对分离度
}

//+------------------------------------------------------------------+
//| 计算均线组斜率                                                    |
//+------------------------------------------------------------------+
double CalculateGroupSlope(const double &values[])
{
   if(ArraySize(values) == 0)
      return 0.0;
   
   //--- 计算均线组的平均值
   double current_avg = 0.0;
   for(int i = 0; i < ArraySize(values); i++)
   {
      current_avg += values[i];
   }
   current_avg /= ArraySize(values);
   
   //--- 获取前一根K线的均线组平均值
   double prev_long_group_values[];
   if(!gmma_indicator.GetLongGroupValues(prev_long_group_values, 1))
   {
      return 0.0;
   }
   
   double prev_avg = 0.0;
   for(int i = 0; i < ArraySize(prev_long_group_values); i++)
   {
      prev_avg += prev_long_group_values[i];
   }
   prev_avg /= ArraySize(prev_long_group_values);
   
   return (current_avg - prev_avg) / prev_avg; // 相对斜率
}

//+------------------------------------------------------------------+
//| 处理交易逻辑                                                      |
//+------------------------------------------------------------------+
void ProcessTradingLogic()
{
   //--- 检查是否有持仓
   bool has_position = trade_manager.HasPosition();
   
   if(!has_position)
   {
      //--- 无持仓时检查入场信号
      if(current_market_state == TREND_BULL)
      {
         if(CheckEntrySignal())
         {
            ExecuteLongEntry();
         }
      }
   }
   else
   {
      //--- 有持仓时检查出场信号
      CheckExitSignals();
   }
}

//+------------------------------------------------------------------+
//| 检查入场信号                                                      |
//+------------------------------------------------------------------+
bool CheckEntrySignal()
{
   return signal_manager.CheckLongEntrySignal(&gmma_indicator);
}

//+------------------------------------------------------------------+
//| 执行做多入场                                                      |
//+------------------------------------------------------------------+
void ExecuteLongEntry()
{
   //--- 检查点差
   if(!trade_manager.CheckSpread())
   {
      return;
   }
   
   //--- 计算仓位大小
   double position_size = risk_manager.CalculatePositionSize();
   if(position_size <= 0)
   {
      Print("仓位计算错误");
      return;
   }
   
   //--- 计算止损价格
   double stop_loss = trade_manager.CalculateStopLoss(ORDER_TYPE_BUY);
   
   //--- 执行开仓
   if(trade_manager.OpenLongPosition(position_size, stop_loss))
   {
      Print("多单开仓成功，手数：", position_size, "，止损：", stop_loss);
   }
}

//+------------------------------------------------------------------+
//| 检查出场信号                                                      |
//+------------------------------------------------------------------+
void CheckExitSignals()
{
   //--- 检查趋势衰竭信号
   if(current_market_state == TREND_RANGE)
   {
      trade_manager.CloseAllPositions("趋势衰竭");
      return;
   }
   
   //--- 检查明确反转信号
   if(signal_manager.CheckExitSignal(&gmma_indicator))
   {
      trade_manager.CloseAllPositions("反转信号");
      return;
   }
}

//+------------------------------------------------------------------+
//| 绘制GMMA线条                                                      |
//+------------------------------------------------------------------+
void DrawGmmaLines()
{
   //--- 创建短期组线条
   string short_periods[];
   StringSplit(GmmaShortPeriods, ',', short_periods);
   
   for(int i = 0; i < ArraySize(short_periods); i++)
   {
      string obj_name = "GMMA_Short_" + short_periods[i];
      ObjectCreate(0, obj_name, OBJ_TREND, 0, 0, 0, 0, 0);
      ObjectSetInteger(0, obj_name, OBJPROP_COLOR, ShortGroupColor);
      ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 1);
   }
   
   //--- 创建长期组线条
   string long_periods[];
   StringSplit(GmmaLongPeriods, ',', long_periods);
   
   for(int i = 0; i < ArraySize(long_periods); i++)
   {
      string obj_name = "GMMA_Long_" + long_periods[i];
      ObjectCreate(0, obj_name, OBJ_TREND, 0, 0, 0, 0, 0);
      ObjectSetInteger(0, obj_name, OBJPROP_COLOR, LongGroupColor);
      ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 2);
   }
}

//+------------------------------------------------------------------+
//| 更新GMMA线条显示                                                  |
//+------------------------------------------------------------------+
void UpdateGmmaLines()
{
   //--- 更新短期组线条
   double short_group_values[];
   if(gmma_indicator.GetShortGroupValues(short_group_values))
   {
      string short_periods[];
      StringSplit(GmmaShortPeriods, ',', short_periods);
      
      for(int i = 0; i < ArraySize(short_periods) && i < ArraySize(short_group_values); i++)
      {
         string obj_name = "GMMA_Short_" + short_periods[i];
         // 这里需要实现线条的实时更新逻辑
      }
   }
   
   //--- 更新长期组线条
   double long_group_values[];
   if(gmma_indicator.GetLongGroupValues(long_group_values))
   {
      string long_periods[];
      StringSplit(GmmaLongPeriods, ',', long_periods);
      
      for(int i = 0; i < ArraySize(long_periods) && i < ArraySize(long_group_values); i++)
      {
         string obj_name = "GMMA_Long_" + long_periods[i];
         // 这里需要实现线条的实时更新逻辑
      }
   }
}

//+------------------------------------------------------------------+
//| 更新界面面板                                                      |
//+------------------------------------------------------------------+
void UpdatePanel()
{
   //--- 更新市场状态
   string market_state_text = "";
   switch(current_market_state)
   {
      case TREND_BULL:  market_state_text = "牛市"; break;
      case TREND_BEAR:  market_state_text = "熊市"; break;
      case TREND_RANGE: market_state_text = "震荡"; break;
   }
   
   //--- 更新风险设置
   double current_risk = risk_manager.GetCurrentRiskPercent();
   string risk_mode = risk_manager.GetRiskMode();
   
   //--- 更新连胜连亏信息
   int win_streak = risk_manager.GetWinStreak();
   int lose_streak = risk_manager.GetLoseStreak();
   
   //--- 更新持仓信息
   string position_info = trade_manager.GetPositionInfo();
   
   //--- 构建显示文本
   string display_text = StringFormat(
      "=== GMMA Trend Follower EA v1.0 ===\n" +
      "市场状态: %s\n" +
      "风险设置: %.1f%% (%s)\n" +
      "连胜/连亏: +%d / -%d\n" +
      "持仓状态: %s\n" +
      "魔法数字: %d\n" +
      "时间: %s",
      market_state_text,
      current_risk,
      risk_mode,
      win_streak,
      lose_streak,
      position_info,
      MagicNumber,
      TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES)
   );
   
   //--- 显示在图表上
   Comment(display_text);
}

//+------------------------------------------------------------------+
//| Chart event function                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
   if(ShowPanel)
   {
      ui_panel.OnEvent(id, lparam, dparam, sparam);
   }
}

//+------------------------------------------------------------------+
//| Trade event function                                             |
//+------------------------------------------------------------------+
void OnTrade()
{
   //--- 更新风险管理器的交易统计
   risk_manager.UpdateTradeStatistics();
}