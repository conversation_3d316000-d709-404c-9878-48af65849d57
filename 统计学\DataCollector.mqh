//+------------------------------------------------------------------+
//|                                             DataCollector.mqh |
//|                                  数据收集和缓存管理模块          |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Professional Volatility Dashboard"
#property link      ""

#include "Defines.mqh"

//+------------------------------------------------------------------+
//| 数据收集器类                                                     |
//+------------------------------------------------------------------+
class CDataCollector
{
private:
    // 缓存的价格数据
    double            m_cachedHigh[];
    double            m_cachedLow[];
    double            m_cachedClose[];
    datetime          m_cachedTime[];
    
    // 缓存状态
    int               m_cachedBars;
    datetime          m_lastUpdateTime;
    
    // 历史数据缓存
    struct SHistoricalData
    {
        datetime      date;
        double        high;
        double        low;
        double        close;
        double        range;
    };
    
    SHistoricalData   m_dailyData[];
    int               m_dailyDataSize;
    
    // 内部方法
    bool              UpdateDailyCache();
    bool              ValidateData(int rates_total, const datetime &time[], 
                                 const double &high[], const double &low[], 
                                 const double &close[]);
    
public:
    CDataCollector();
    ~CDataCollector();
    
    // 更新数据
    bool              UpdateData(int rates_total, const datetime &time[], 
                                const double &high[], const double &low[], 
                                const double &close[]);
    
    // 获取缓存的数据
    bool              GetCachedData(double &high[], double &low[], 
                                   double &close[], datetime &time[], int &size);
    
    // 获取历史日线数据
    bool              GetDailyData(int days, SHistoricalData &data[]);
    
    // 获取指定时间范围的数据
    bool              GetTimeRangeData(datetime startTime, datetime endTime,
                                      double &high[], double &low[], int &count);
    
    // 清理缓存
    void              ClearCache();
    
    // 获取缓存状态
    bool              IsCacheValid();
    datetime          GetLastUpdateTime();
};

//+------------------------------------------------------------------+
//| 构造函数                                                         |
//+------------------------------------------------------------------+
CDataCollector::CDataCollector()
{
    m_cachedBars = 0;
    m_lastUpdateTime = 0;
    m_dailyDataSize = 0;
    
    ArrayResize(m_cachedHigh, 0);
    ArrayResize(m_cachedLow, 0);
    ArrayResize(m_cachedClose, 0);
    ArrayResize(m_cachedTime, 0);
    ArrayResize(m_dailyData, 0);
}

//+------------------------------------------------------------------+
//| 析构函数                                                         |
//+------------------------------------------------------------------+
CDataCollector::~CDataCollector()
{
    ClearCache();
}

//+------------------------------------------------------------------+
//| 更新数据                                                         |
//+------------------------------------------------------------------+
bool CDataCollector::UpdateData(int rates_total, const datetime &time[], 
                                const double &high[], const double &low[], 
                                const double &close[])
{
    if(!ValidateData(rates_total, time, high, low, close))
    {
        Print("数据收集器：数据验证失败");
        return false;
    }
    
    // 检查是否需要更新缓存
    if(m_cachedBars == rates_total && m_lastUpdateTime > 0)
    {
        // 只检查最新的几根K线是否有变化
        bool needUpdate = false;
        int checkBars = MathMin(10, rates_total);
        
        for(int i = 0; i < checkBars; i++)
        {
            int index = rates_total - 1 - i;
            int cachedIndex = m_cachedBars - 1 - i;
            
            if(cachedIndex >= 0 && cachedIndex < ArraySize(m_cachedHigh))
            {
                if(MathAbs(m_cachedHigh[cachedIndex] - high[index]) > _Point ||
                   MathAbs(m_cachedLow[cachedIndex] - low[index]) > _Point ||
                   MathAbs(m_cachedClose[cachedIndex] - close[index]) > _Point)
                {
                    needUpdate = true;
                    break;
                }
            }
            else
            {
                needUpdate = true;
                break;
            }
        }
        
        if(!needUpdate)
            return true;
    }
    
    // 更新缓存数组大小
    if(ArrayResize(m_cachedHigh, rates_total) < 0 ||
       ArrayResize(m_cachedLow, rates_total) < 0 ||
       ArrayResize(m_cachedClose, rates_total) < 0 ||
       ArrayResize(m_cachedTime, rates_total) < 0)
    {
        Print("数据收集器：无法调整缓存数组大小");
        return false;
    }
    
    // 复制数据到缓存
    if(ArrayCopy(m_cachedHigh, high, 0, 0, rates_total) < 0 ||
       ArrayCopy(m_cachedLow, low, 0, 0, rates_total) < 0 ||
       ArrayCopy(m_cachedClose, close, 0, 0, rates_total) < 0 ||
       ArrayCopy(m_cachedTime, time, 0, 0, rates_total) < 0)
    {
        Print("数据收集器：数据复制失败");
        return false;
    }
    
    m_cachedBars = rates_total;
    m_lastUpdateTime = TimeCurrent();
    
    // 更新日线数据缓存
    UpdateDailyCache();
    
    return true;
}

//+------------------------------------------------------------------+
//| 获取缓存的数据                                                   |
//+------------------------------------------------------------------+
bool CDataCollector::GetCachedData(double &high[], double &low[], 
                                   double &close[], datetime &time[], int &size)
{
    if(m_cachedBars <= 0)
        return false;
    
    if(ArrayResize(high, m_cachedBars) < 0 ||
       ArrayResize(low, m_cachedBars) < 0 ||
       ArrayResize(close, m_cachedBars) < 0 ||
       ArrayResize(time, m_cachedBars) < 0)
        return false;
    
    if(ArrayCopy(high, m_cachedHigh) < 0 ||
       ArrayCopy(low, m_cachedLow) < 0 ||
       ArrayCopy(close, m_cachedClose) < 0 ||
       ArrayCopy(time, m_cachedTime) < 0)
        return false;
    
    size = m_cachedBars;
    return true;
}

//+------------------------------------------------------------------+
//| 获取历史日线数据                                                 |
//+------------------------------------------------------------------+
bool CDataCollector::GetDailyData(int days, SHistoricalData &data[])
{
    if(days <= 0)
        return false;
    
    int availableDays = MathMin(days, m_dailyDataSize);
    if(availableDays <= 0)
        return false;
    
    if(ArrayResize(data, availableDays) < 0)
        return false;
    
    // 复制最近的日线数据
    for(int i = 0; i < availableDays; i++)
    {
        int sourceIndex = m_dailyDataSize - availableDays + i;
        if(sourceIndex >= 0 && sourceIndex < m_dailyDataSize)
        {
            data[i] = m_dailyData[sourceIndex];
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 获取指定时间范围的数据                                           |
//+------------------------------------------------------------------+
bool CDataCollector::GetTimeRangeData(datetime startTime, datetime endTime,
                                      double &high[], double &low[], int &count)
{
    if(startTime >= endTime || m_cachedBars <= 0)
        return false;
    
    // 查找时间范围内的数据
    int startIndex = -1, endIndex = -1;
    
    for(int i = 0; i < m_cachedBars; i++)
    {
        if(m_cachedTime[i] >= startTime && startIndex == -1)
            startIndex = i;
        if(m_cachedTime[i] <= endTime)
            endIndex = i;
    }
    
    if(startIndex == -1 || endIndex == -1 || startIndex > endIndex)
        return false;
    
    count = endIndex - startIndex + 1;
    
    if(ArrayResize(high, count) < 0 || ArrayResize(low, count) < 0)
        return false;
    
    // 复制指定范围的数据
    for(int i = 0; i < count; i++)
    {
        high[i] = m_cachedHigh[startIndex + i];
        low[i] = m_cachedLow[startIndex + i];
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 更新日线数据缓存                                                 |
//+------------------------------------------------------------------+
bool CDataCollector::UpdateDailyCache()
{
    // 获取日线数据
    int dailyBars = iBars(_Symbol, PERIOD_D1);
    if(dailyBars <= 0)
        return false;
    
    // 限制缓存的日线数据量
    int maxDays = 500;
    int cacheDays = MathMin(dailyBars, maxDays);
    
    if(ArrayResize(m_dailyData, cacheDays) < 0)
        return false;
    
    // 填充日线数据
    for(int i = 0; i < cacheDays; i++)
    {
        int barIndex = cacheDays - 1 - i;
        
        m_dailyData[i].date = iTime(_Symbol, PERIOD_D1, barIndex);
        m_dailyData[i].high = iHigh(_Symbol, PERIOD_D1, barIndex);
        m_dailyData[i].low = iLow(_Symbol, PERIOD_D1, barIndex);
        m_dailyData[i].close = iClose(_Symbol, PERIOD_D1, barIndex);
        m_dailyData[i].range = m_dailyData[i].high - m_dailyData[i].low;
    }
    
    m_dailyDataSize = cacheDays;
    return true;
}

//+------------------------------------------------------------------+
//| 验证数据                                                         |
//+------------------------------------------------------------------+
bool CDataCollector::ValidateData(int rates_total, const datetime &time[], 
                                 const double &high[], const double &low[], 
                                 const double &close[])
{
    if(rates_total <= 0)
        return false;
    
    if(ArraySize(time) < rates_total ||
       ArraySize(high) < rates_total ||
       ArraySize(low) < rates_total ||
       ArraySize(close) < rates_total)
        return false;
    
    // 检查最新几根K线的数据有效性
    int checkBars = MathMin(10, rates_total);
    for(int i = 0; i < checkBars; i++)
    {
        int index = rates_total - 1 - i;
        
        if(high[index] <= 0 || low[index] <= 0 || close[index] <= 0)
            return false;
        
        if(high[index] < low[index])
            return false;
        
        if(close[index] < low[index] || close[index] > high[index])
            return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 清理缓存                                                         |
//+------------------------------------------------------------------+
void CDataCollector::ClearCache()
{
    ArrayResize(m_cachedHigh, 0);
    ArrayResize(m_cachedLow, 0);
    ArrayResize(m_cachedClose, 0);
    ArrayResize(m_cachedTime, 0);
    ArrayResize(m_dailyData, 0);
    
    m_cachedBars = 0;
    m_lastUpdateTime = 0;
    m_dailyDataSize = 0;
}

//+------------------------------------------------------------------+
//| 获取缓存状态                                                     |
//+------------------------------------------------------------------+
bool CDataCollector::IsCacheValid()
{
    return (m_cachedBars > 0 && m_lastUpdateTime > 0);
}

//+------------------------------------------------------------------+
//| 获取最后更新时间                                                 |
//+------------------------------------------------------------------+
datetime CDataCollector::GetLastUpdateTime()
{
    return m_lastUpdateTime;
}