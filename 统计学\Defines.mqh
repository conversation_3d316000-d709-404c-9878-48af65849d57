//+------------------------------------------------------------------+
//|                                                      Defines.mqh |
//|                                  常量定义和数据结构              |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Professional Volatility Dashboard"
#property link      ""

#ifndef DEFINES_MQH
#define DEFINES_MQH

//+------------------------------------------------------------------+
//| 枚举定义                                                         |
//+------------------------------------------------------------------+

// 波动率状态枚举
enum ENUM_VOLATILITY_STATUS
{
    VOLATILITY_HEATING_UP,      // 升温中
    VOLATILITY_COOLING_DOWN,    // 降温中
    VOLATILITY_CONSOLIDATING    // 盘整中
};

// 面板区域枚举
enum ENUM_PANEL_SECTION
{
    PANEL_SECTION_MACRO,        // 宏观区域
    PANEL_SECTION_MESO,         // 中观区域
    PANEL_SECTION_MICRO         // 微观区域
};

// 交易时段枚举
enum ENUM_TRADING_SESSION
{
    SESSION_ASIA,               // 亚洲时段
    SESSION_EUROPE,             // 欧洲时段
    SESSION_US_EUROPE_CROSS,    // 欧美重叠
    SESSION_US_NIGHT,           // 美盘尾盘
    SESSION_NONE                // 无交易时段
};

//+------------------------------------------------------------------+
//| 配置结构体                                                       |
//+------------------------------------------------------------------+

// 面板配置结构体
struct SPanelConfig
{
    ENUM_BASE_CORNER corner;        // 面板位置
    int              xOffset;       // X偏移
    int              yOffset;       // Y偏移
    color            panelColor;    // 面板颜色
    color            textColor;     // 文本颜色
    int              fontSize;      // 字体大小
    string           indicatorName; // 指标名称
};

// 宏观分析配置结构体
struct SMacroConfig
{
    int              slowPeriod;    // 慢速周期
    int              fastPeriod;    // 快速周期
};

// 中观分析配置结构体
struct SMesoConfig
{
    int              historyDays;   // 历史天数
    int              blockHours;    // 时间区块小时数
};

// 微观分析配置结构体
struct SMicroConfig
{
    int              slowPeriod;    // 慢速周期
    int              fastPeriod;    // 快速周期
    color            heatingColor;  // 升温颜色
    color            coolingColor;  // 降温颜色
    color            normalColor;   // 正常颜色
};

//+------------------------------------------------------------------+
//| 数据结构体                                                       |
//+------------------------------------------------------------------+

// 宏观分析数据结构体
struct SMacroData
{
    double           slowADR;           // 慢速ADR
    double           fastADR;           // 快速ADR
    double           todayRange;        // 今日波幅
    double           remainingSpace;    // 剩余空间
    double           completionPercent; // 完成度百分比
    bool             isValid;           // 数据有效性
    datetime         updateTime;        // 更新时间
};

// 中观分析数据结构体
struct SMesoData
{
    double           currentBlockVolatility;  // 当前时段波动率
    double           previousBlockVolatility; // 前序时段波动率
    int              currentBlockStart;       // 当前时段开始小时
    int              currentBlockEnd;         // 当前时段结束小时
    int              previousBlockStart;      // 前序时段开始小时
    int              previousBlockEnd;        // 前序时段结束小时
    bool             isValid;                 // 数据有效性
    datetime         updateTime;              // 更新时间
};

// 微观分析数据结构体
struct SMicroData
{
    double                  fastAvgRange;     // 快速平均波幅
    double                  slowAvgRange;     // 慢速平均波幅
    double                  volatilityRatio;  // 波动率比率
    ENUM_VOLATILITY_STATUS  status;           // 波动率状态
    color                   statusColor;      // 状态颜色
    bool                    isValid;          // 数据有效性
    datetime                updateTime;       // 更新时间
};

//+------------------------------------------------------------------+
//| 常量定义                                                         |
//+------------------------------------------------------------------+

// 面板尺寸常量
#define PANEL_WIDTH         220
#define PANEL_HEIGHT        180
#define SECTION_HEIGHT      55
#define LINE_HEIGHT         14
#define MARGIN_X            8
#define MARGIN_Y            6

// 颜色常量
#define COLOR_PANEL_BG      C'45,45,45'
#define COLOR_SECTION_BG    C'35,35,35'
#define COLOR_BORDER        C'70,130,180'
#define COLOR_TITLE         C'255,255,255'
#define COLOR_TEXT          C'211,211,211'
#define COLOR_VALUE         C'135,206,250'

// 字体常量
#define FONT_NAME           "Arial"
#define FONT_SIZE_TITLE     9
#define FONT_SIZE_TEXT      8
#define FONT_SIZE_VALUE     8

// 对象名称前缀
#define OBJ_PREFIX_PANEL    "VD_Panel_"
#define OBJ_PREFIX_SECTION  "VD_Section_"
#define OBJ_PREFIX_TEXT     "VD_Text_"

//+------------------------------------------------------------------+
//| 工具函数宏                                                       |
//+------------------------------------------------------------------+

// 生成唯一对象名称
#define GENERATE_OBJ_NAME(prefix, suffix) (prefix + suffix + "_" + (string)ChartID())

// 安全删除对象
#define SAFE_DELETE_OBJECT(name) \
    if(ObjectFind(0, name) >= 0) \
        ObjectDelete(0, name)

// 点数转换
#define POINTS_TO_PRICE(points) (points * _Point * ((_Digits == 5 || _Digits == 3) ? 10 : 1))
#define PRICE_TO_POINTS(price) (price / _Point / ((_Digits == 5 || _Digits == 3) ? 10 : 1))

#endif // DEFINES_MQH


<replace_in_file>
<path>统计学/Defines.mqh</path>
<diff>
//+------------------------------------------------------------------+
//|                                                      Defines.mqh |
//|                                  常量定义和数据结构              |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Professional Volatility Dashboard"
#property link      ""

#ifndef DEFINES_MQH
#define DEFINES_MQH

//+------------------------------------------------------------------+
//| 枚举定义                                                         |
//+------------------------------------------------------------------+

// 波动率状态枚举
enum ENUM_VOLATILITY_STATUS
{
    VOLATILITY_HEATING_UP,      // 升温中
    VOLATILITY_COOLING_DOWN,    // 降温中
    VOLATILITY_CONSOLIDATING    // 盘整中
};

// 面板区域枚举
enum ENUM_PANEL_SECTION
{
    PANEL_SECTION_MACRO,        // 宏观区域
    PANEL_SECTION_MESO,         // 中观区域
    PANEL_SECTION_MICRO         // 微观区域
};


//+------------------------------------------------------------------+
//| 配置结构体                                                       |
//+------------------------------------------------------------------+

// 面板配置结构体
struct SPanelConfig
{
    ENUM_BASE_CORNER corner;        // 面板位置
    int              xOffset;       // X偏移
    int              yOffset;       // Y偏移
    color            panelColor;    // 面板颜色
    color            textColor;     // 文本颜色
    int              fontSize;      // 字体大小
    string           indicatorName; // 指标名称
};

// 宏观分析配置结构体
struct SMacroConfig
{
    int              slowPeriod;    // 慢速周期
    int              fastPeriod;    // 快速周期
};

// 中观分析配置结构体
struct SMesoConfig
{
    int              historyDays;   // 历史天数
    int              blockHours;    // 时间区块小时数
};

// 微观分析配置结构体
struct SMicroConfig
{
    int              slowPeriod;    // 慢速周期
    int              fastPeriod;    // 快速周期
    color            heatingColor;  // 升温颜色
    color            coolingColor;  // 降温颜色
    color            normalColor;   // 正常颜色
};

//+------------------------------------------------------------------+
//| 数据结构体                                                       |
//+------------------------------------------------------------------+

// 宏观分析数据结构体
struct SMacroData
{
    double           slowADR;           // 慢速ADR
    double           fastADR;           // 快速ADR
    double           todayRange;        // 今日波幅
    double           remainingSpace;    // 剩余空间
    double           completionPercent; // 完成度百分比
    bool             isValid;           // 数据有效性
    datetime         updateTime;        // 更新时间
};

// 中观分析数据结构体
struct SMesoData
{
    double           currentBlockVolatility;  // 当前时段波动率
    double           previousBlockVolatility; // 前序时段波动率
    int              currentBlockStart;       // 当前时段开始小时
    int              currentBlockEnd;         // 当前时段结束小时
    int              previousBlockStart;      // 前序时段开始小时
    int              previousBlockEnd;        // 前序时段结束小时
    bool             isValid;                 // 数据有效性
    datetime         updateTime;              // 更新时间
};

// 微观分析数据结构体
struct SMicroData
{
    double                  fastAvgRange;     // 快速平均波幅
    double                  slowAvgRange;     // 慢速平均波幅
    double                  volatilityRatio;  // 波动率比率
    ENUM_VOLATILITY_STATUS  status;           // 波动率状态
    color                   statusColor;      // 状态颜色
    bool                    isValid;          // 数据有效性
    datetime                updateTime;       // 更新时间
};

//+------------------------------------------------------------------+
//| 常量定义                                                         |
//+------------------------------------------------------------------+

// 面板尺寸常量
#define PANEL_WIDTH         220
#define PANEL_HEIGHT        180
#define SECTION_HEIGHT      55
#define LINE_HEIGHT         14
#define MARGIN_X            8
#define MARGIN_Y            6

// 颜色常量
#define COLOR_PANEL_BG      C'45,45,45'
#define COLOR_SECTION_BG    C'35,35,35'
#define COLOR_BORDER        C'70,130,180'
#define COLOR_TITLE         C'255,255,255'
#define COLOR_TEXT          C'211,211,211'
#define COLOR_VALUE         C'135,206,250'

// 字体常量
#define FONT_NAME           "Arial"
#define FONT_SIZE_TITLE     9
#define FONT_SIZE_TEXT      8
#define FONT_SIZE_VALUE     8

// 对象名称前缀
#define OBJ_PREFIX_PANEL    "VD_Panel_"
#define OBJ_PREFIX_SECTION  "VD_Section_"
#define OBJ_PREFIX_TEXT     "VD_Text_"

//+------------------------------------------------------------------+
//| 工具函数宏                                                       |
//+------------------------------------------------------------------+

// 生成唯一对象名称
#define GENERATE_OBJ_NAME(prefix, suffix) (prefix + suffix + "_" + (string)ChartID())

// 安全删除对象
#define SAFE_DELETE_OBJECT(name) \
    if(ObjectFind(0, name) >= 0) \
        ObjectDelete(0, name)

// 点数转换
#define POINTS_TO_PRICE(points) (points * _Point * ((_Digits == 5 || _Digits == 3) ? 10 : 1))
#define PRICE_TO_POINTS(price) (price / _Point / ((_Digits == 5 || _Digits == 3) ? 10 : 1))

#endif // DEFINES_MQH
