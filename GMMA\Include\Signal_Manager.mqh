//+------------------------------------------------------------------+
//|                                             Signal_Manager.mqh |
//|                                        GMMA信号识别和管理模块    |
//+------------------------------------------------------------------+
#property copyright "Signal Manager Module"
#property version   "1.00"

#include "GMMA_Indicator.mqh"

//+------------------------------------------------------------------+
//| 信号管理器类                                                      |
//+------------------------------------------------------------------+
class CSignal_Manager
{
private:
   //--- 信号状态
   bool              m_initialized;
   datetime          m_last_signal_time;
   
   //--- 信号参数
   double            m_min_separation_ratio;    // 最小分离比例
   double            m_convergence_threshold;   // 收敛阈值
   int               m_confirmation_bars;       // 确认K线数量
   
public:
   //--- 构造函数和析构函数
                     CSignal_Manager();
                    ~CSignal_Manager();
   
   //--- 初始化和清理
   bool              Initialize();
   void              Cleanup();
   
   //--- 信号检测
   bool              CheckLongEntrySignal(CGMMA_Indicator* gmma);
   bool              CheckExitSignal(CGMMA_Indicator* gmma);
   
   //--- 辅助方法
   bool              IsShortGroupNearLongGroup(CGMMA_Indicator* gmma);
   bool              IsShortGroupRecovering(CGMMA_Indicator* gmma);
   bool              IsShortGroupCrossingDown(CGMMA_Indicator* gmma);
   
private:
   //--- 内部计算方法
   double            CalculateGroupDistance(const double &short_values[], const double &long_values[]);
   bool              IsGroupConverging(const double &values[], int bars_back = 3);
   bool              IsGroupDiverging(const double &values[], int bars_back = 3);
   double            CalculateGroupSlope(const double &values[], int bars_back = 2);
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
CSignal_Manager::CSignal_Manager()
{
   m_initialized = false;
   m_last_signal_time = 0;
   m_min_separation_ratio = 0.001;  // 0.1%
   m_convergence_threshold = 0.0005; // 0.05%
   m_confirmation_bars = 2;
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
CSignal_Manager::~CSignal_Manager()
{
   Cleanup();
}

//+------------------------------------------------------------------+
//| 初始化信号管理器                                                  |
//+------------------------------------------------------------------+
bool CSignal_Manager::Initialize()
{
   Print("初始化信号管理器...");
   
   m_initialized = true;
   m_last_signal_time = 0;
   
   Print("信号管理器初始化完成");
   return true;
}

//+------------------------------------------------------------------+
//| 清理资源                                                          |
//+------------------------------------------------------------------+
void CSignal_Manager::Cleanup()
{
   m_initialized = false;
}

//+------------------------------------------------------------------+
//| 检查做多入场信号                                                  |
//+------------------------------------------------------------------+
bool CSignal_Manager::CheckLongEntrySignal(CGMMA_Indicator* gmma)
{
   if(!m_initialized || gmma == NULL || !gmma.IsInitialized())
      return false;
   
   //--- 1. 检查短期组是否已回落至长期组附近
   if(!IsShortGroupNearLongGroup(gmma))
   {
      return false;
   }
   
   //--- 2. 检查价格是否触及长期组但未有效跌破
   double short_values[], long_values[];
   if(!gmma.GetAllValues(short_values, long_values, 0))
      return false;
   
   double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double long_group_min = long_values[ArraySize(long_values)-1]; // 长期组最下方均线
   
   // 价格应该在长期组最下方均线附近或之上
   if(current_price < long_group_min * 0.999) // 允许0.1%的穿透
   {
      return false;
   }
   
   //--- 3. 检查短期组是否停止下跌并出现向上迹象
   if(!IsShortGroupRecovering(gmma))
   {
      return false;
   }
   
   //--- 4. 避免重复信号（同一根K线只发一次信号）
   datetime current_bar_time = iTime(Symbol(), PERIOD_CURRENT, 0);
   if(current_bar_time == m_last_signal_time)
   {
      return false;
   }
   
   //--- 所有条件满足，记录信号时间
   m_last_signal_time = current_bar_time;
   Print("检测到做多入场信号");
   
   return true;
}

//+------------------------------------------------------------------+
//| 检查出场信号                                                      |
//+------------------------------------------------------------------+
bool CSignal_Manager::CheckExitSignal(CGMMA_Indicator* gmma)
{
   if(!m_initialized || gmma == NULL || !gmma.IsInitialized())
      return false;
   
   //--- 检查短期组是否整体向下穿越长期组
   return IsShortGroupCrossingDown(gmma);
}

//+------------------------------------------------------------------+
//| 检查短期组是否接近长期组                                          |
//+------------------------------------------------------------------+
bool CSignal_Manager::IsShortGroupNearLongGroup(CGMMA_Indicator* gmma)
{
   double short_values[], long_values[];
   if(!gmma.GetAllValues(short_values, long_values, 0))
      return false;
   
   //--- 计算两组之间的距离
   double distance = CalculateGroupDistance(short_values, long_values);
   
   //--- 距离应该足够小，表示短期组已回落至长期组附近
   return (distance < m_convergence_threshold);
}

//+------------------------------------------------------------------+
//| 检查短期组是否正在恢复                                            |
//+------------------------------------------------------------------+
bool CSignal_Manager::IsShortGroupRecovering(CGMMA_Indicator* gmma)
{
   double short_values_current[], short_values_prev[];
   
   if(!gmma.GetShortGroupValues(short_values_current, 0) || 
      !gmma.GetShortGroupValues(short_values_prev, 1))
      return false;
   
   //--- 检查短期组最快的均线（周期最小）是否开始上穿较慢的均线
   // 假设数组按周期从小到大排序
   if(ArraySize(short_values_current) < 2)
      return false;
   
   double fast_ma_current = short_values_current[0];  // 最快均线当前值
   double slow_ma_current = short_values_current[1];  // 较慢均线当前值
   double fast_ma_prev = short_values_prev[0];       // 最快均线前值
   double slow_ma_prev = short_values_prev[1];       // 较慢均线前值
   
   //--- 检查是否出现金叉（快线上穿慢线）
   bool golden_cross = (fast_ma_prev <= slow_ma_prev) && (fast_ma_current > slow_ma_current);
   
   //--- 或者检查短期组整体斜率是否转为向上
   double slope = CalculateGroupSlope(short_values_current);
   bool upward_slope = slope > 0;
   
   return golden_cross || upward_slope;
}

//+------------------------------------------------------------------+
//| 检查短期组是否向下穿越长期组                                      |
//+------------------------------------------------------------------+
bool CSignal_Manager::IsShortGroupCrossingDown(CGMMA_Indicator* gmma)
{
   double short_values[], long_values[];
   double short_values_prev[], long_values_prev[];
   
   if(!gmma.GetAllValues(short_values, long_values, 0) ||
      !gmma.GetAllValues(short_values_prev, long_values_prev, 1))
      return false;
   
   //--- 计算短期组和长期组的平均值
   double short_avg_current = 0, short_avg_prev = 0;
   double long_avg_current = 0, long_avg_prev = 0;
   
   for(int i = 0; i < ArraySize(short_values); i++)
   {
      short_avg_current += short_values[i];
      short_avg_prev += short_values_prev[i];
   }
   short_avg_current /= ArraySize(short_values);
   short_avg_prev /= ArraySize(short_values_prev);
   
   for(int i = 0; i < ArraySize(long_values); i++)
   {
      long_avg_current += long_values[i];
      long_avg_prev += long_values_prev[i];
   }
   long_avg_current /= ArraySize(long_values);
   long_avg_prev /= ArraySize(long_values_prev);
   
   //--- 检查是否发生死叉（短期组平均值下穿长期组平均值）
   bool death_cross = (short_avg_prev >= long_avg_prev) && (short_avg_current < long_avg_current);
   
   if(death_cross)
   {
      Print("检测到短期组下穿长期组，出场信号");
   }
   
   return death_cross;
}

//+------------------------------------------------------------------+
//| 计算两组均线之间的距离                                            |
//+------------------------------------------------------------------+
double CSignal_Manager::CalculateGroupDistance(const double &short_values[], const double &long_values[])
{
   if(ArraySize(short_values) == 0 || ArraySize(long_values) == 0)
      return 1.0; // 返回大值表示距离很远
   
   //--- 计算短期组平均值
   double short_avg = 0;
   for(int i = 0; i < ArraySize(short_values); i++)
   {
      short_avg += short_values[i];
   }
   short_avg /= ArraySize(short_values);
   
   //--- 计算长期组平均值
   double long_avg = 0;
   for(int i = 0; i < ArraySize(long_values); i++)
   {
      long_avg += long_values[i];
   }
   long_avg /= ArraySize(long_values);
   
   //--- 返回相对距离
   return MathAbs(short_avg - long_avg) / long_avg;
}

//+------------------------------------------------------------------+
//| 检查均线组是否收敛                                                |
//+------------------------------------------------------------------+
bool CSignal_Manager::IsGroupConverging(const double &values[], int bars_back = 3)
{
   if(ArraySize(values) < 2 || bars_back < 2)
      return false;
   
   //--- 计算当前分离度
   double max_val = values[0], min_val = values[0];
   for(int i = 1; i < ArraySize(values); i++)
   {
      if(values[i] > max_val) max_val = values[i];
      if(values[i] < min_val) min_val = values[i];
   }
   double current_separation = (max_val - min_val) / min_val;
   
   //--- 这里应该比较历史分离度，简化处理返回基于当前分离度的判断
   return current_separation < m_convergence_threshold;
}

//+------------------------------------------------------------------+
//| 检查均线组是否发散                                                |
//+------------------------------------------------------------------+
bool CSignal_Manager::IsGroupDiverging(const double &values[], int bars_back = 3)
{
   return !IsGroupConverging(values, bars_back);
}

//+------------------------------------------------------------------+
//| 计算均线组斜率                                                    |
//+------------------------------------------------------------------+
double CSignal_Manager::CalculateGroupSlope(const double &values[], int bars_back = 2)
{
   if(ArraySize(values) == 0)
      return 0.0;
   
   //--- 计算当前平均值
   double current_avg = 0;
   for(int i = 0; i < ArraySize(values); i++)
   {
      current_avg += values[i];
   }
   current_avg /= ArraySize(values);
   
   //--- 简化处理：基于最快均线的变化来估算斜率
   //--- 简化处理：基于最快均线的变化来估算斜率
   // 实际应用中应该获取历史数据进行比较
   if(ArraySize(values) > 0)
   {
      double fast_ma = values[0]; // 假设第一个是最快的均线
      return (current_avg > fast_ma) ? 0.001 : -0.001; // 简化的斜率计算
   }
   
   return 0.0;
}
