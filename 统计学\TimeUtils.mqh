//+------------------------------------------------------------------+
//|                                                    TimeUtils.mqh |
//|                                  动态概率统计分析系统时间工具模块 |
//|                                                                  |
//+------------------------------------------------------------------+

#ifndef TIME_UTILS_MQH
#define TIME_UTILS_MQH

#include "Defines.mqh"



//+------------------------------------------------------------------+
//| 获取服务器时间偏移量（相对于GMT的小时数）                        |
//+------------------------------------------------------------------+
int Time_GetServerGMTOffset()
  {
   // 这个函数可以用来获取服务器相对于GMT的偏移量
   // 你可以根据你的MT5服务器实际时区来调整这个值
   // 例如：如果服务器在GMT+2，返回2；如果在GMT-5，返回-5
   return 0; // 默认假设服务器时间为GMT+0，你可以根据实际情况修改
  }

//+------------------------------------------------------------------+
//| 获取精确的北京时间（考虑服务器时区偏移）                         |
//+------------------------------------------------------------------+
datetime Time_GetAccurateBeijingTime()
  {
   // 获取MT5服务器时间
   datetime server_time = TimeCurrent();
   
   // 获取服务器GMT偏移量
   int server_gmt_offset = Time_GetServerGMTOffset();
   
   // 计算北京时间：服务器时间 - 服务器偏移 + 北京偏移(+8)
   datetime beijing_time = server_time - (server_gmt_offset * 3600) + (8 * 3600);
   
   return beijing_time;
  }

//+------------------------------------------------------------------+
//| 格式化时间为字符串                                               |
//+------------------------------------------------------------------+
string Time_Format(const datetime dt)
  {
   // 格式化为 YYYY.MM.DD HH:MI:SS 格式
   return TimeToString(dt, TIME_DATE|TIME_SECONDS);
  }

//+------------------------------------------------------------------+
//| 获取交易时段                                                     |
//+------------------------------------------------------------------+
ENUM_TRADING_SESSION Time_GetSession(const datetime beijing_time, string &session_string)
  {
   // 将时间分解为结构体
   MqlDateTime dt_struct;
   TimeToStruct(beijing_time, dt_struct);
   
   int hour = dt_struct.hour;
   
   // 根据小时判断交易时段
   if(hour >= 7 && hour <= 14)
     {
      // 亚盘: 07:00 - 14:59
      session_string = "亚盘时段";
      return SESSION_ASIA;
     }
   else if(hour >= 15 && hour < 20)
     {
      // 欧盘: 15:00 - 19:59
      session_string = "欧盘时段";
      return SESSION_EUROPE;
     }
   else if(hour >= 20 && hour <= 23)
     {
      // 欧美交叉: 20:00 - 23:59
      session_string = "欧美交叉";
      return SESSION_US_EUROPE_CROSS;
     }
   else
     {
      // 美盘尾盘: 00:00 - 06:59 (删除休市时段，美盘尾盘延续到6:59)
      session_string = "美盘尾盘";
      return SESSION_US_NIGHT;
     }
  }

//+------------------------------------------------------------------+
//| 获取时段中文描述                                                 |
//+------------------------------------------------------------------+
string Time_GetSessionDescription(const ENUM_TRADING_SESSION session)
  {
   switch(session)
     {
      case SESSION_ASIA:
         return "亚盘时段 (07:00-14:59)";
      case SESSION_EUROPE:
         return "欧盘时段 (15:00-19:59)";
      case SESSION_US_EUROPE_CROSS:
         return "欧美交叉时段 (20:00-23:59)";
      case SESSION_US_NIGHT:
         return "美盘尾盘 (00:00-06:59)";
      case SESSION_NONE:
      default:
         return "未知时段";
     }
  }

//+------------------------------------------------------------------+
//| 判断是否为交易时段                                               |
//+------------------------------------------------------------------+
bool Time_IsTradingSession(const ENUM_TRADING_SESSION session)
  {
   // 现在所有时段都是交易时段，没有休市时段
   return true;
  }

//+------------------------------------------------------------------+
//| 获取时段颜色                                                     |
//+------------------------------------------------------------------+
color Time_GetSessionColor(const ENUM_TRADING_SESSION session)
  {
   switch(session)
     {
      case SESSION_ASIA:
         return clrYellow;        // 亚盘 - 黄色
      case SESSION_EUROPE:
         return clrLightBlue;     // 欧盘 - 浅蓝色
      case SESSION_US_EUROPE_CROSS:
         return clrLightGreen;    // 欧美交叉 - 浅绿色
      case SESSION_US_NIGHT:
         return clrOrange;        // 美盘后半夜 - 橙色
      case SESSION_NONE:
      default:
         return clrGray;          // 休市 - 灰色
     }
  }

#endif // TIME_UTILS_MQH