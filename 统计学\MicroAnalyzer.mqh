//+------------------------------------------------------------------+
//|                                              MicroAnalyzer.mqh |
//|                                  微观执行分析模块                |
//|                                  实时波动率状态分析              |
//+------------------------------------------------------------------+
#property copyright "Professional Volatility Dashboard"
#property link      ""

#include "Defines.mqh"

//+------------------------------------------------------------------+
//| 微观分析器类                                                     |
//+------------------------------------------------------------------+
class CMicroAnalyzer
{
private:
    SMicroConfig      m_config;
    SMicroData        m_cachedData;
    datetime          m_lastCalculateTime;
    
    // 计算平均波幅
    double            CalculateAverageRange(int period);
    
    // 确定波动率状态
    ENUM_VOLATILITY_STATUS DetermineVolatilityStatus(double fastRange, double slowRange);
    
    // 获取状态颜色
    color             GetStatusColor(ENUM_VOLATILITY_STATUS status);
    
public:
    CMicroAnalyzer();
    ~CMicroAnalyzer();
    
    // 初始化配置
    bool              Initialize(const SMicroConfig &config);
    
    // 执行微观分析计算
    bool              Calculate(SMicroData &data);
    
    // 获取缓存的数据
    bool              GetCachedData(SMicroData &data);
};

//+------------------------------------------------------------------+
//| 构造函数                                                         |
//+------------------------------------------------------------------+
CMicroAnalyzer::CMicroAnalyzer()
{
    m_lastCalculateTime = 0;
    ZeroMemory(m_cachedData);
}

//+------------------------------------------------------------------+
//| 析构函数                                                         |
//+------------------------------------------------------------------+
CMicroAnalyzer::~CMicroAnalyzer()
{
}

//+------------------------------------------------------------------+
//| 初始化配置                                                       |
//+------------------------------------------------------------------+
bool CMicroAnalyzer::Initialize(const SMicroConfig &config)
{
    if(config.slowPeriod <= 0 || config.fastPeriod <= 0)
    {
        Print("微观分析器：无效的周期参数");
        return false;
    }
    
    if(config.fastPeriod >= config.slowPeriod)
    {
        Print("微观分析器：快速周期应小于慢速周期");
        return false;
    }
    
    m_config = config;
    return true;
}

//+------------------------------------------------------------------+
//| 执行微观分析计算                                                 |
//+------------------------------------------------------------------+
bool CMicroAnalyzer::Calculate(SMicroData &data)
{
    // 微观分析需要实时更新，但可以限制更新频率
    datetime currentTime = TimeCurrent();
    if(currentTime - m_lastCalculateTime < 30 && m_cachedData.isValid) // 30秒更新一次
    {
        data = m_cachedData;
        return true;
    }
    
    // 计算快速和慢速平均波幅
    double fastAvgRange = CalculateAverageRange(m_config.fastPeriod);
    double slowAvgRange = CalculateAverageRange(m_config.slowPeriod);
    
    if(fastAvgRange <= 0 || slowAvgRange <= 0)
    {
        Print("微观分析器：平均波幅计算失败");
        return false;
    }
    
    // 计算波动率比率
    double volatilityRatio = fastAvgRange / slowAvgRange;
    
    // 确定波动率状态
    ENUM_VOLATILITY_STATUS status = DetermineVolatilityStatus(fastAvgRange, slowAvgRange);
    
    // 获取状态颜色
    color statusColor = GetStatusColor(status);
    
    // 填充数据结构
    data.fastAvgRange = fastAvgRange;
    data.slowAvgRange = slowAvgRange;
    data.volatilityRatio = volatilityRatio;
    data.status = status;
    data.statusColor = statusColor;
    data.isValid = true;
    data.updateTime = currentTime;
    
    // 缓存数据
    m_cachedData = data;
    m_lastCalculateTime = currentTime;
    
    return true;
}

//+------------------------------------------------------------------+
//| 获取缓存的数据                                                   |
//+------------------------------------------------------------------+
bool CMicroAnalyzer::GetCachedData(SMicroData &data)
{
    if(!m_cachedData.isValid)
        return false;
        
    data = m_cachedData;
    return true;
}

//+------------------------------------------------------------------+
//| 计算平均波幅                                                     |
//+------------------------------------------------------------------+
double CMicroAnalyzer::CalculateAverageRange(int period)
{
    if(period <= 0)
        return 0;
    
    // 使用当前时间框架计算ATR
    double atrValue = iATR(_Symbol, _Period, period, 1);
    
    if(atrValue == EMPTY_VALUE || atrValue <= 0)
    {
        Print("微观分析器：ATR计算失败，周期=", period);
        return 0;
    }
    
    // 转换为点数
    double pointValue = _Point;
    if(_Digits == 5 || _Digits == 3)
        pointValue *= 10;
    
    return atrValue / pointValue;
}

//+------------------------------------------------------------------+
//| 确定波动率状态                                                   |
//+------------------------------------------------------------------+
ENUM_VOLATILITY_STATUS CMicroAnalyzer::DetermineVolatilityStatus(double fastRange, double slowRange)
{
    if(fastRange <= 0 || slowRange <= 0)
        return VOLATILITY_CONSOLIDATING;
    
    double ratio = fastRange / slowRange;
    
    // 定义阈值
    double heatingThreshold = 1.2;   // 快速波幅比慢速波幅高20%以上为升温
    double coolingThreshold = 0.8;   // 快速波幅比慢速波幅低20%以上为降温
    
    if(ratio > heatingThreshold)
        return VOLATILITY_HEATING_UP;
    else if(ratio < coolingThreshold)
        return VOLATILITY_COOLING_DOWN;
    else
        return VOLATILITY_CONSOLIDATING;
}

//+------------------------------------------------------------------+
//| 获取状态颜色                                                     |
//+------------------------------------------------------------------+
color CMicroAnalyzer::GetStatusColor(ENUM_VOLATILITY_STATUS status)
{
    switch(status)
    {
        case VOLATILITY_HEATING_UP:
            return m_config.heatingColor;
        case VOLATILITY_COOLING_DOWN:
            return m_config.coolingColor;
        case VOLATILITY_CONSOLIDATING:
        default:
            return m_config.normalColor;
    }
}