# GMMA全自动EA交易程序开发

## Core Features

- 12条EMA的GMMA指标计算（短期组：3,5,8,10,12,15；长期组：30,35,40,45,50,60）

- 市场状态分析（TREND_BULL/TREND_BEAR/TREND_RANGE）

- 精确入场信号识别（短期组回调至长期组附近的反弹）

- 动态仓位计算（基于ATR和风险百分比）

- 赢冲输缩资金管理系统

- 自动止损和趋势衰竭出场

- 可视化界面显示EA状态和GMMA线条

- 模块化架构设计

## Tech Stack

{
  "Web": null,
  "iOS": null,
  "Android": null,
  "Other": "MQL5语言，MetaTrader 5平台，Trade.mqh交易库，ATR指标，EMA指标，模块化函数设计"
}

## Design

专业交易EA界面，深色主题，左上角显示EA状态信息（市场状态、风险设置、连胜连亏次数、持仓状态），图表上绘制12条GMMA均线（短期组蓝色系，长期组红色系），清晰的信号标识

## Plan

Note: 

- [ ] is holding
- [/] is doing
- [X] is done

---

[X] 创建项目结构和主EA文件框架

[X] 实现GMMA指标计算模块，包含12条EMA的计算和更新

[X] 开发信号识别算法，检测短期组与长期组的交叉、分离、收敛模式

[X] 构建交易管理模块，实现自动开仓和平仓逻辑

[X] 实现风险管理系统，包含止损止盈和资金管理功能

[X] 创建可视化界面面板，显示GMMA状态和交易信息

[X] 开发参数配置系统，支持动态参数调节

[X] 集成所有模块并进行系统测试

[X] 编译EA并修复所有语法错误

[ ] 实现多时间框架分析功能

[ ] 优化性能和添加错误处理机制
