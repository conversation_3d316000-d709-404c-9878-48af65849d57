//+------------------------------------------------------------------+
//|                                                    UI_Panel.mqh |
//|                                              用户界面面板模块    |
//+------------------------------------------------------------------+
#property copyright "UI Panel Module"
#property version   "1.00"

#include <Controls\Dialog.mqh>
#include <Controls\Button.mqh>
#include <Controls\Label.mqh>
#include <Controls\Panel.mqh>

//+------------------------------------------------------------------+
//| UI面板类                                                          |
//+------------------------------------------------------------------+
class CUI_Panel : public CAppDialog
{
private:
   //--- 控件
   CLabel            m_label_title;             // 标题标签
   CLabel            m_label_market_state;      // 市场状态标签
   CLabel            m_label_risk_info;         // 风险信息标签
   CLabel            m_label_streak_info;       // 连胜连亏信息标签
   CLabel            m_label_position_info;     // 仓位信息标签
   CLabel            m_label_time_info;         // 时间信息标签
   
   CButton           m_button_start_stop;       // 启停按钮
   CButton           m_button_close_all;        // 平仓按钮
   
   //--- 面板参数
   int               m_panel_width;             // 面板宽度
   int               m_panel_height;            // 面板高度
   color             m_background_color;        // 背景颜色
   color             m_text_color;              // 文字颜色
   
   //--- 状态标志
   bool              m_initialized;
   bool              m_ea_enabled;              // EA启用状态
   
public:
   //--- 构造函数和析构函数
                     CUI_Panel();
                    ~CUI_Panel();
   
   //--- 创建和销毁
   virtual bool      Create(const long chart_id = 0, const string name = "GMMA_Panel", 
                           const int subwin = 0, const int x1 = 10, const int y1 = 30);
   virtual void      Destroy(const int reason = REASON_PROGRAM);
   
   //--- 事件处理
   virtual bool      OnEvent(const int id, const long &lparam, const double &dparam, const string &sparam);
   
   //--- 更新方法
   void              UpdateMarketState(string state, color state_color = clrWhite);
   void              UpdateRiskInfo(double risk_percent, string risk_mode);
   void              UpdateStreakInfo(int win_streak, int lose_streak);
   void              UpdatePositionInfo(string position_info);
   void              UpdateTimeInfo(string time_info);
   void              UpdateEAStatus(bool enabled);
   
protected:
   //--- 创建控件
   bool              CreateTitle();
   bool              CreateLabels();
   bool              CreateButtons();
   
   //--- 按钮事件处理
   void              OnClickStartStop();
   void              OnClickCloseAll();
   
   //--- 界面更新
   void              UpdateButtonStates();
   void              SetLabelText(CLabel& label, string text, color text_color = clrWhite);
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
CUI_Panel::CUI_Panel()
{
   m_panel_width = 300;
   m_panel_height = 200;
   m_background_color = C'30,30,30';  // 深灰色背景
   m_text_color = clrWhite;
   m_initialized = false;
   m_ea_enabled = true;
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
CUI_Panel::~CUI_Panel()
{
   Destroy();
}

//+------------------------------------------------------------------+
//| 创建面板                                                          |
//+------------------------------------------------------------------+
bool CUI_Panel::Create(const long chart_id = 0, const string name = "GMMA_Panel", 
                      const int subwin = 0, const int x1 = 10, const int y1 = 30)
{
   if(!CAppDialog::Create(chart_id, name, subwin, x1, y1, x1 + m_panel_width, y1 + m_panel_height))
   {
      Print("面板创建失败");
      return false;
   }
   
   //--- 设置面板属性
   // Background(m_background_color); // 移除无效的Background调用
   
   //--- 创建控件
   if(!CreateTitle() || !CreateLabels() || !CreateButtons())
   {
      Print("控件创建失败");
      return false;
   }
   
   m_initialized = true;
   Print("UI面板创建成功");
   
   return true;
}

//+------------------------------------------------------------------+
//| 销毁面板                                                          |
//+------------------------------------------------------------------+
void CUI_Panel::Destroy(const int reason = REASON_PROGRAM)
{
   if(m_initialized)
   {
      CAppDialog::Destroy(reason);
      m_initialized = false;
   }
}

//+------------------------------------------------------------------+
//| 事件处理                                                          |
//+------------------------------------------------------------------+
bool CUI_Panel::OnEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
   //--- 按钮点击事件
   if(id == CHARTEVENT_OBJECT_CLICK)
   {
      if(sparam == m_button_start_stop.Name())
      {
         OnClickStartStop();
         return true;
      }
      else if(sparam == m_button_close_all.Name())
      {
         OnClickCloseAll();
         return true;
      }
   }
   
   return CAppDialog::OnEvent(id, lparam, dparam, sparam);
}

//+------------------------------------------------------------------+
//| 创建标题                                                          |
//+------------------------------------------------------------------+
bool CUI_Panel::CreateTitle()
{
   if(!m_label_title.Create(m_chart_id, "GMMA_Title", m_subwin, 10, 10, 290, 30))
      return false;
   
   m_label_title.Text("GMMA Trend Follower EA v1.0");
   m_label_title.Color(clrGold);
   m_label_title.FontSize(12);
   
   if(!Add(m_label_title))
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| 创建标签                                                          |
//+------------------------------------------------------------------+
bool CUI_Panel::CreateLabels()
{
   int y_pos = 40;
   int line_height = 20;
   
   //--- 市场状态标签
   if(!m_label_market_state.Create(m_chart_id, "GMMA_MarketState", m_subwin, 10, y_pos, 290, y_pos + line_height))
      return false;
   m_label_market_state.Text("市场状态: 初始化中...");
   m_label_market_state.Color(m_text_color);
   if(!Add(m_label_market_state))
      return false;
   
   y_pos += line_height + 5;
   
   //--- 风险信息标签
   if(!m_label_risk_info.Create(m_chart_id, "GMMA_RiskInfo", m_subwin, 10, y_pos, 290, y_pos + line_height))
      return false;
   m_label_risk_info.Text("风险设置: 2.0% (常规模式)");
   m_label_risk_info.Color(m_text_color);
   if(!Add(m_label_risk_info))
      return false;
   
   y_pos += line_height + 5;
   
   //--- 连胜连亏信息标签
   if(!m_label_streak_info.Create(m_chart_id, "GMMA_StreakInfo", m_subwin, 10, y_pos, 290, y_pos + line_height))
      return false;
   m_label_streak_info.Text("连胜/连亏: +0 / -0");
   m_label_streak_info.Color(m_text_color);
   if(!Add(m_label_streak_info))
      return false;
   
   y_pos += line_height + 5;
   
   //--- 仓位信息标签
   if(!m_label_position_info.Create(m_chart_id, "GMMA_PositionInfo", m_subwin, 10, y_pos, 290, y_pos + line_height))
      return false;
   m_label_position_info.Text("持仓状态: 无仓位");
   m_label_position_info.Color(m_text_color);
   if(!Add(m_label_position_info))
      return false;
   
   y_pos += line_height + 5;
   
   //--- 时间信息标签
   if(!m_label_time_info.Create(m_chart_id, "GMMA_TimeInfo", m_subwin, 10, y_pos, 290, y_pos + line_height))
      return false;
   m_label_time_info.Text("时间: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
   m_label_time_info.Color(m_text_color);
   if(!Add(m_label_time_info))
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| 创建按钮                                                          |
//+------------------------------------------------------------------+
bool CUI_Panel::CreateButtons()
{
   int y_pos = 160;
   int button_width = 80;
   int button_height = 25;
   
   //--- 启停按钮
   if(!m_button_start_stop.Create(m_chart_id, "GMMA_StartStop", m_subwin, 10, y_pos, 10 + button_width, y_pos + button_height))
      return false;
   m_button_start_stop.Text("暂停EA");
   m_button_start_stop.Color(clrWhite);
   m_button_start_stop.ColorBackground(C'0,128,0'); // 绿色背景
   if(!Add(m_button_start_stop))
      return false;
   
   //--- 平仓按钮
   if(!m_button_close_all.Create(m_chart_id, "GMMA_CloseAll", m_subwin, 100, y_pos, 100 + button_width, y_pos + button_height))
      return false;
   m_button_close_all.Text("全部平仓");
   m_button_close_all.Color(clrWhite);
   m_button_close_all.ColorBackground(C'128,0,0'); // 红色背景
   if(!Add(m_button_close_all))
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| 更新市场状态                                                      |
//+------------------------------------------------------------------+
void CUI_Panel::UpdateMarketState(string state, color state_color = clrWhite)
{
   if(!m_initialized)
      return;
   
   string text = "市场状态: " + state;
   SetLabelText(m_label_market_state, text, state_color);
}

//+------------------------------------------------------------------+
//| 更新风险信息                                                      |
//+------------------------------------------------------------------+
void CUI_Panel::UpdateRiskInfo(double risk_percent, string risk_mode)
{
   if(!m_initialized)
      return;
   
   string text = StringFormat("风险设置: %.1f%% (%s)", risk_percent, risk_mode);
   color text_color = clrWhite;
   
   if(risk_mode == "赢冲模式")
      text_color = clrLime;
   else if(risk_mode == "输缩模式")
      text_color = clrOrange;
   
   SetLabelText(m_label_risk_info, text, text_color);
}

//+------------------------------------------------------------------+
//| 更新连胜连亏信息                                                  |
//+------------------------------------------------------------------+
void CUI_Panel::UpdateStreakInfo(int win_streak, int lose_streak)
{
   if(!m_initialized)
      return;
   
   string text = StringFormat("连胜/连亏: +%d / -%d", win_streak, lose_streak);
   color text_color = clrWhite;
   
   if(win_streak >= 3)
      text_color = clrLime;
   else if(lose_streak >= 2)
      text_color = clrRed;
   
   SetLabelText(m_label_streak_info, text, text_color);
}

//+------------------------------------------------------------------+
//| 更新仓位信息                                                      |
//+------------------------------------------------------------------+
void CUI_Panel::UpdatePositionInfo(string position_info)
{
   if(!m_initialized)
      return;
   
   string text = "持仓状态: " + position_info;
   color text_color = clrWhite;
   
   if(StringFind(position_info, "多头") >= 0)
      text_color = clrLime;
   else if(StringFind(position_info, "空头") >= 0)
      text_color = clrRed;
   
   SetLabelText(m_label_position_info, text, text_color);
}

//+------------------------------------------------------------------+
//| 更新时间信息                                                      |
//+------------------------------------------------------------------+
void CUI_Panel::UpdateTimeInfo(string time_info)
{
   if(!m_initialized)
      return;
   
   string text = "时间: " + time_info;
   SetLabelText(m_label_time_info, text, m_text_color);
}

//+------------------------------------------------------------------+
//| 更新EA状态                                                        |
//+------------------------------------------------------------------+
void CUI_Panel::UpdateEAStatus(bool enabled)
{
   if(!m_initialized)
      return;
   
   m_ea_enabled = enabled;
   UpdateButtonStates();
}

//+------------------------------------------------------------------+
//| 启停按钮点击事件                                                  |
//+------------------------------------------------------------------+
void CUI_Panel::OnClickStartStop()
{
   m_ea_enabled = !m_ea_enabled;
   UpdateButtonStates();
   
   string status = m_ea_enabled ? "启动" : "暂停";
   Print("EA状态切换: ", status);
   
   //--- 这里可以添加全局变量来控制EA的运行状态
   GlobalVariableSet("GMMA_EA_Enabled", m_ea_enabled ? 1 : 0);
}

//+------------------------------------------------------------------+
//| 平仓按钮点击事件                                                  |
//+------------------------------------------------------------------+
void CUI_Panel::OnClickCloseAll()
{
   Print("用户请求平掉所有仓位");
   
   //--- 设置全局变量标志，让EA主程序处理平仓
   GlobalVariableSet("GMMA_Close_All_Positions", 1);
   
   //--- 显示确认信息
   Alert("平仓指令已发送，EA将在下一个Tick处理");
}

//+------------------------------------------------------------------+
//| 更新按钮状态                                                      |
//+------------------------------------------------------------------+
void CUI_Panel::UpdateButtonStates()
{
   if(!m_initialized)
      return;
   
   if(m_ea_enabled)
   {
      m_button_start_stop.Text("暂停EA");
      m_button_start_stop.ColorBackground(C'0,128,0'); // 绿色
   }
   else
   {
      m_button_start_stop.Text("启动EA");
      m_button_start_stop.ColorBackground(C'128,128,0'); // 黄色
   }
   
   //--- 刷新按钮显示
   ChartRedraw();
}

//+------------------------------------------------------------------+
//| 设置标签文本                                                      |
//+------------------------------------------------------------------+
void CUI_Panel::SetLabelText(CLabel& label, string text, color text_color = clrWhite)
{
   if(!m_initialized)
      return;
   
   label.Text(text);
   label.Color(text_color);
   ChartRedraw();
}
