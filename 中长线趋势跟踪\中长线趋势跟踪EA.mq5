//+------------------------------------------------------------------+
//|                                    中长线趋势跟踪系统.mq5        |
//|                          基于20/10均线突破的趋势跟踪策略         |
//|                                             Created by CodeBuddy |
//+------------------------------------------------------------------+
#property copyright "CodeBuddy"
#property link      ""
#property version   "1.00"
#property description "多单:有效上破20均线K的高点处多开，回撤两个ATR止损，有效下破10均线K的低点空平"
#property description "空单:有效下破20均线K的低点处空开，回撤两个ATR止损，有效上破10均线K的高点多平"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

//--- 交易对象
CTrade trade;
CPositionInfo position;
CAccountInfo account;

//+------------------------------------------------------------------+
//| 输入参数
//+------------------------------------------------------------------+
input group           "=== 核心策略参数 ==="
input ulong           MagicNumber = ********;       // EA魔术数字
input double          RiskPercent = 1.0;            // 风险百分比(%)

input group           "=== 均线参数 ==="
input int             MA20_Period = 20;             // 开仓周期均线
input int             MA10_Period = 10;             // 止盈周期均线
input int             MA60_Period = 60;             // 趋势确认均线
input int             MA90_Period = 90;             // 季度线
input int             MA180_Period = 180;           // 牛熊分界线
input ENUM_MA_METHOD  MA_Method = MODE_EMA;         // 均线类型
input ENUM_APPLIED_PRICE MA_Price = PRICE_CLOSE;    // 均线价格

input group           "=== 趋势过滤 ==="
input bool            UseTrendFilter = true;        // 启用趋势过滤
input bool            UseMA60Filter = true;         // 启用MA60走平过滤
input bool            UseMA90Filter = true;         // 启用MA90季度线过滤
input bool            UseMA180Filter = false;       // 启用MA180牛熊线过滤

input group           "=== ATR止损参数 ==="
input int             ATR_Period = 14;              // ATR周期
input double          ATR_Multiplier = 2.0;         // ATR止损倍数
input bool            UseTrailingStop = true;       // 启用动态止损
input double          TrailingStopDistance = 1.5;   // 动态止损距离(ATR倍数)
input double          TrailingStepSize = 0.5;       // 动态止损步长(ATR倍数)
input bool            UseBreakEvenStop = true;      // 启用保本止损
input double          BreakEvenTrigger = 1.0;       // 保本触发距离(ATR倍数)

input group           "=== 交易方向控制 ==="
enum TRADE_DIRECTION {
    TRADE_BOTH = 0,     // 多空都做
    TRADE_LONG_ONLY = 1, // 只做多
    TRADE_SHORT_ONLY = 2 // 只做空
};
input TRADE_DIRECTION TradeDirection = TRADE_BOTH;  // 交易方向

input group           "=== 时间过滤 ==="
input bool            UseTimeFilter = false;        // 启用时间过滤
input int             StartHour = 9;                // 开始交易时间(小时)
input int             EndHour = 17;                 // 结束交易时间(小时)
input bool            AvoidNews = true;             // 避开重要新闻时段

input group           "=== 资金管理 ==="
input bool            UseMoneyManagement = true;    // 启用资金管理
input double          FixedLotSize = 0.1;           // 固定手数(关闭资金管理时使用)
input double          MaxRiskPerTrade = 1.0;        // 单笔最大风险(%)

input group           "=== 风险控制 ==="
input bool            UseRiskControl = true;        // 启用风险控制
input double          MaxDailyLoss = 5.0;           // 每日最大亏损(%)
input double          MaxDrawdownLimit = 10.0;      // 最大回撤限制(%)
input int             MaxConsecutiveLosses = 3;     // 最大连续亏损次数
input bool            StopAfterTarget = false;      // 达到目标后停止交易
input double          DailyProfitTarget = 3.0;      // 每日盈利目标(%)

//--- 全局变量
int ma20_handle, ma10_handle, ma60_handle, ma90_handle, ma180_handle, atr_handle;
datetime last_bar_time = 0;
bool long_signal_active = false;
bool short_signal_active = false;

//--- 交易统计变量
int total_trades = 0;
int winning_trades = 0;
int losing_trades = 0;
double total_profit = 0.0;
double max_profit = 0.0;
double max_loss = 0.0;
double max_drawdown = 0.0;
double peak_balance = 0.0;
int consecutive_losses = 0;
double daily_start_balance = 0.0;
datetime last_reset_date = 0;
bool trading_stopped = false;

//+------------------------------------------------------------------+
//| 专家初始化函数
//+------------------------------------------------------------------+
int OnInit() {
    //--- 设置交易参数
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(10);
    trade.SetTypeFilling(ORDER_FILLING_FOK);
    
    //--- 初始化指标句柄
    ma20_handle = iMA(Symbol(), PERIOD_CURRENT, MA20_Period, 0, MA_Method, MA_Price);
    ma10_handle = iMA(Symbol(), PERIOD_CURRENT, MA10_Period, 0, MA_Method, MA_Price);
    ma60_handle = iMA(Symbol(), PERIOD_CURRENT, MA60_Period, 0, MA_Method, MA_Price);
    ma90_handle = iMA(Symbol(), PERIOD_CURRENT, MA90_Period, 0, MA_Method, MA_Price);
    ma180_handle = iMA(Symbol(), PERIOD_CURRENT, MA180_Period, 0, MA_Method, MA_Price);
    atr_handle = iATR(Symbol(), PERIOD_CURRENT, ATR_Period);
    
    //--- 检查句柄有效性
    if (ma20_handle == INVALID_HANDLE || ma10_handle == INVALID_HANDLE || 
        ma60_handle == INVALID_HANDLE || ma90_handle == INVALID_HANDLE || 
        ma180_handle == INVALID_HANDLE || atr_handle == INVALID_HANDLE) {
        Print("❌ 错误: 指标初始化失败！");
        return INIT_FAILED;
    }
    
    Print("✅ 中长线趋势跟踪系统初始化成功");
    Print("📊 交易品种: ", Symbol());
    Print("💰 风险百分比: ", RiskPercent, "%");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| 专家反初始化函数
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    //--- 释放指标句柄
    IndicatorRelease(ma20_handle);
    IndicatorRelease(ma10_handle);
    IndicatorRelease(ma60_handle);
    IndicatorRelease(ma90_handle);
    IndicatorRelease(ma180_handle);
    IndicatorRelease(atr_handle);
    
    Print("✅ 中长线趋势跟踪系统已停止");
}

//+------------------------------------------------------------------+
//| 专家Tick函数
//+------------------------------------------------------------------+
void OnTick() {
    //--- 检查是否有新K线
    if (!IsNewBar()) return;
    
    //--- 获取指标数据
    double ma20[], ma10[], ma60[], ma90[], ma180[], atr[];
    if (!GetIndicatorData(ma20, ma10, ma60, ma90, ma180, atr)) return;
    
    //--- 获取价格数据
    double high[], low[], close[];
    if (!GetPriceData(high, low, close)) return;
    
    //--- 清理无效挂单
    CleanupInvalidOrders();
    
    //--- 执行交易逻辑
    CheckLongSignals(ma20, ma10, ma60, ma90, ma180, atr, high, low, close);
    CheckShortSignals(ma20, ma10, ma60, ma90, ma180, atr, high, low, close);
    
    //--- 管理现有持仓和挂单
    ManagePositions(ma20, ma10, high, low);
    
    //--- 更新信息面板
    ShowInfoPanel();
}

//+------------------------------------------------------------------+
//| 清理无效挂单
//+------------------------------------------------------------------+
void CleanupInvalidOrders() {
    for (int i = OrdersTotal() - 1; i >= 0; i--) {
        ulong ticket = OrderGetTicket(i);
        if (ticket <= 0) continue;
        
        if (OrderSelect(ticket) && 
            OrderGetString(ORDER_SYMBOL) == Symbol() && 
            OrderGetInteger(ORDER_MAGIC) == MagicNumber) {
            
            // 检查挂单是否已经过期或无效
            datetime order_time = (datetime)OrderGetInteger(ORDER_TIME_SETUP);
            datetime current_time = TimeCurrent();
            
            // 如果挂单超过24小时未成交，删除它
            if (current_time - order_time > 24 * 3600) {
                trade.OrderDelete(ticket);
                Print("🗑️ 删除过期挂单: ", ticket);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 检查多单信号
//+------------------------------------------------------------------+
void CheckLongSignals(const double &ma20[], const double &ma10[], const double &ma60[], 
                      const double &ma90[], const double &ma180[], const double &atr[], 
                      const double &high[], const double &low[], const double &close[]) {
    //--- 检查交易方向限制
    if (TradeDirection == TRADE_SHORT_ONLY) return;
    
    //--- 风险控制检查
    if (!IsRiskControlOK()) return;
    
    //--- 时间过滤检查
    if (!IsTimeToTrade()) return;
    
    //--- 趋势过滤检查
    if (UseTrendFilter && !CheckLongTrendFilter(ma60, ma90, ma180, close)) return;
    
    //--- 检查多单开仓信号：有效上破20日均线
    if (!HasLongPosition() && !HasPendingBuyStop()) {
        // 检查是否有效上破20均线：收盘价突破20均线
        if (close[1] > ma20[1] && close[2] <= ma20[2]) {
            // 在突破K线的高点之上挂Buy Stop单
            double entry_price = high[1] + SymbolInfoDouble(Symbol(), SYMBOL_POINT) * 10; // 高点上方10点
            double stop_loss = entry_price - (atr[1] * ATR_Multiplier);
            double lot_size = CalculateLotSize(entry_price, stop_loss);
            
            if (lot_size > 0 && entry_price > SymbolInfoDouble(Symbol(), SYMBOL_ASK)) {
                // 挂Buy Stop单
                if (trade.BuyStop(lot_size, entry_price, Symbol(), stop_loss, 0, ORDER_TIME_GTC, 0, "Long_BuyStop")) {
                    Print("🟢 多单挂单: 入场=", entry_price, " 止损=", stop_loss, " 手数=", lot_size);
                    Print("📊 突破20均线K线高点: ", high[1], " 挂单价格: ", entry_price);
                    long_signal_active = true;
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 检查空单信号
//+------------------------------------------------------------------+
void CheckShortSignals(const double &ma20[], const double &ma10[], const double &ma60[], 
                       const double &ma90[], const double &ma180[], const double &atr[], 
                       const double &high[], const double &low[], const double &close[]) {
    //--- 检查交易方向限制
    if (TradeDirection == TRADE_LONG_ONLY) return;
    
    //--- 时间过滤检查
    if (!IsTimeToTrade()) return;
    
    //--- 趋势过滤检查
    if (UseTrendFilter && !CheckShortTrendFilter(ma60, ma90, ma180, close)) return;
    
    //--- 检查空单开仓信号：有效下破20日均线
    if (!HasShortPosition() && !HasPendingSellStop()) {
        // 检查是否有效下破20均线：收盘价跌破20均线
        if (close[1] < ma20[1] && close[2] >= ma20[2]) {
            // 在突破K线的低点之下挂Sell Stop单
            double entry_price = low[1] - SymbolInfoDouble(Symbol(), SYMBOL_POINT) * 10; // 低点下方10点
            double stop_loss = entry_price + (atr[1] * ATR_Multiplier);
            double lot_size = CalculateLotSize(entry_price, stop_loss);
            
            if (lot_size > 0 && entry_price < SymbolInfoDouble(Symbol(), SYMBOL_BID)) {
                // 挂Sell Stop单
                if (trade.SellStop(lot_size, entry_price, Symbol(), stop_loss, 0, ORDER_TIME_GTC, 0, "Short_SellStop")) {
                    Print("🔴 空单挂单: 入场=", entry_price, " 止损=", stop_loss, " 手数=", lot_size);
                    Print("📊 跌破20均线K线低点: ", low[1], " 挂单价格: ", entry_price);
                    short_signal_active = true;
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 管理现有持仓
//+------------------------------------------------------------------+
void ManagePositions(const double &ma20[], const double &ma10[], 
                     const double &high[], const double &low[]) {
    // 获取收盘价数据用于确认突破
    double close[];
    ArraySetAsSeries(close, true);
    if (CopyClose(Symbol(), PERIOD_CURRENT, 0, 3, close) < 3) return;
    
    // 获取ATR数据用于动态止损
    double atr[];
    ArraySetAsSeries(atr, true);
    if (CopyBuffer(atr_handle, 0, 0, 3, atr) < 3) return;
    
    // 管理挂单
    ManagePendingOrders(ma20, ma10, close);
    
    // 管理持仓
    for (int i = PositionsTotal() - 1; i >= 0; i--) {
        if (!position.SelectByIndex(i)) continue;
        if (position.Symbol() != Symbol() || position.Magic() != MagicNumber) continue;
        
        // 执行动态止损管理
        if (UseTrailingStop || UseBreakEvenStop) {
            ManageTrailingStop(position.Ticket(), atr[1]);
        }
        
        if (position.PositionType() == POSITION_TYPE_BUY) {
            //--- 多单平仓条件：有效下破10均线，在K线低点之下挂单平仓
            if (close[1] < ma10[1] && close[2] >= ma10[2]) {
                // 在跌破10均线K线的低点之下挂单平仓
                double exit_price = low[1] - SymbolInfoDouble(Symbol(), SYMBOL_POINT) * 5;
                if (trade.PositionClose(position.Ticket())) {
                    Print("🟢 多单平仓: 有效跌破10均线 收盘=", close[1], " 均线=", ma10[1]);
                    long_signal_active = false;
                }
            }
        }
        else if (position.PositionType() == POSITION_TYPE_SELL) {
            //--- 空单平仓条件：有效上破10均线，在K线高点之上挂单平仓
            if (close[1] > ma10[1] && close[2] <= ma10[2]) {
                // 在涨破10均线K线的高点之上挂单平仓
                double exit_price = high[1] + SymbolInfoDouble(Symbol(), SYMBOL_POINT) * 5;
                if (trade.PositionClose(position.Ticket())) {
                    Print("🔴 空单平仓: 有效涨破10均线 收盘=", close[1], " 均线=", ma10[1]);
                    short_signal_active = false;
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 动态止损管理
//+------------------------------------------------------------------+
void ManageTrailingStop(ulong ticket, double current_atr) {
    if (!position.SelectByTicket(ticket)) return;
    
    double current_price = (position.PositionType() == POSITION_TYPE_BUY) ? 
                          SymbolInfoDouble(Symbol(), SYMBOL_BID) : 
                          SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    
    double open_price = position.PriceOpen();
    double current_sl = position.StopLoss();
    double new_sl = current_sl;
    bool modify_needed = false;
    
    if (position.PositionType() == POSITION_TYPE_BUY) {
        // 多单动态止损管理
        double profit_points = current_price - open_price;
        
        // 保本止损：当盈利达到触发距离时，将止损移至开仓价
        if (UseBreakEvenStop && profit_points >= current_atr * BreakEvenTrigger) {
            if (current_sl < open_price) {
                new_sl = open_price + SymbolInfoDouble(Symbol(), SYMBOL_POINT) * 10; // 保本+10点
                modify_needed = true;
                Print("🟡 多单保本止损: 票号=", ticket, " 新止损=", new_sl);
            }
        }
        
        // 动态止损：根据价格移动调整止损
        if (UseTrailingStop && profit_points > 0) {
            double trailing_sl = current_price - (current_atr * TrailingStopDistance);
            
            // 只有当新止损高于当前止损时才修改
            if (trailing_sl > current_sl + (current_atr * TrailingStepSize)) {
                new_sl = trailing_sl;
                modify_needed = true;
                Print("🟢 多单动态止损: 票号=", ticket, " 新止损=", new_sl, " 距离=", 
                      DoubleToString((current_price - new_sl) / SymbolInfoDouble(Symbol(), SYMBOL_POINT), 0), "点");
            }
        }
    }
    else if (position.PositionType() == POSITION_TYPE_SELL) {
        // 空单动态止损管理
        double profit_points = open_price - current_price;
        
        // 保本止损：当盈利达到触发距离时，将止损移至开仓价
        if (UseBreakEvenStop && profit_points >= current_atr * BreakEvenTrigger) {
            if (current_sl > open_price || current_sl == 0) {
                new_sl = open_price - SymbolInfoDouble(Symbol(), SYMBOL_POINT) * 10; // 保本-10点
                modify_needed = true;
                Print("🟡 空单保本止损: 票号=", ticket, " 新止损=", new_sl);
            }
        }
        
        // 动态止损：根据价格移动调整止损
        if (UseTrailingStop && profit_points > 0) {
            double trailing_sl = current_price + (current_atr * TrailingStopDistance);
            
            // 只有当新止损低于当前止损时才修改（空单）
            if (current_sl == 0 || trailing_sl < current_sl - (current_atr * TrailingStepSize)) {
                new_sl = trailing_sl;
                modify_needed = true;
                Print("🔴 空单动态止损: 票号=", ticket, " 新止损=", new_sl, " 距离=", 
                      DoubleToString((new_sl - current_price) / SymbolInfoDouble(Symbol(), SYMBOL_POINT), 0), "点");
            }
        }
    }
    
    // 执行止损修改
    if (modify_needed && new_sl != current_sl) {
        if (!trade.PositionModify(ticket, new_sl, position.TakeProfit())) {
            Print("❌ 动态止损修改失败: 票号=", ticket, " 错误=", GetLastError());
        }
    }
}

//+------------------------------------------------------------------+
//| 管理挂单
//+------------------------------------------------------------------+
void ManagePendingOrders(const double &ma20[], const double &ma10[], const double &close[]) {
    for (int i = OrdersTotal() - 1; i >= 0; i--) {
        ulong ticket = OrderGetTicket(i);
        if (ticket <= 0) continue;
        
        if (OrderSelect(ticket) && 
            OrderGetString(ORDER_SYMBOL) == Symbol() && 
            OrderGetInteger(ORDER_MAGIC) == MagicNumber) {
            
            ENUM_ORDER_TYPE order_type = (ENUM_ORDER_TYPE)OrderGetInteger(ORDER_TYPE);
            
            // 取消过期的挂单（可选：如果趋势反转）
            if (order_type == ORDER_TYPE_BUY_STOP) {
                // 如果价格重新跌破20均线，取消多单挂单
                if (close[1] < ma20[1]) {
                    trade.OrderDelete(ticket);
                    Print("🟡 取消多单挂单: 价格重新跌破20均线");
                    long_signal_active = false;
                }
            }
            else if (order_type == ORDER_TYPE_SELL_STOP) {
                // 如果价格重新涨破20均线，取消空单挂单
                if (close[1] > ma20[1]) {
                    trade.OrderDelete(ticket);
                    Print("🟡 取消空单挂单: 价格重新涨破20均线");
                    short_signal_active = false;
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 风险控制检查
//+------------------------------------------------------------------+
bool IsRiskControlOK() {
    if (!UseRiskControl) return true;
    if (trading_stopped) return false;
    
    // 检查是否需要重置每日统计
    datetime current_time = TimeCurrent();
    MqlDateTime dt;
    TimeToStruct(current_time, dt);
    datetime today_start = StringToTime(IntegerToString(dt.year) + "." + 
                                       IntegerToString(dt.mon) + "." + 
                                       IntegerToString(dt.day) + " 00:00");
    
    if (today_start != last_reset_date) {
        daily_start_balance = account.Balance();
        last_reset_date = today_start;
        consecutive_losses = 0;
    }
    
    // 检查每日最大亏损
    double daily_loss = daily_start_balance - account.Balance();
    double daily_loss_percent = 0.0;
    if (daily_start_balance > 0) {
        daily_loss_percent = (daily_loss / daily_start_balance) * 100.0;
    }
    if (daily_loss_percent >= MaxDailyLoss) {
        Print("⚠️ 风险控制: 达到每日最大亏损限制 ", MaxDailyLoss, "%");
        trading_stopped = true;
        return false;
    }
    
    // 检查最大回撤
    double current_drawdown_percent = 0.0;
    if (peak_balance > 0) {
        current_drawdown_percent = (max_drawdown / peak_balance) * 100.0;
    }
    if (current_drawdown_percent >= MaxDrawdownLimit) {
        Print("⚠️ 风险控制: 达到最大回撤限制 ", MaxDrawdownLimit, "%");
        trading_stopped = true;
        return false;
    }
    
    // 检查连续亏损次数
    if (consecutive_losses >= MaxConsecutiveLosses) {
        Print("⚠️ 风险控制: 达到最大连续亏损次数 ", MaxConsecutiveLosses);
        trading_stopped = true;
        return false;
    }
    
    // 检查每日盈利目标
    if (StopAfterTarget && daily_start_balance > 0) {
        double daily_profit = account.Balance() - daily_start_balance;
        double daily_profit_percent = (daily_profit / daily_start_balance) * 100.0;
        if (daily_profit_percent >= DailyProfitTarget) {
            Print("✅ 风险控制: 达到每日盈利目标 ", DailyProfitTarget, "%");
            trading_stopped = true;
            return false;
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 时间过滤检查
//+------------------------------------------------------------------+
bool IsTimeToTrade() {
    if (!UseTimeFilter) return true;
    
    datetime current_time = TimeCurrent();
    MqlDateTime dt;
    TimeToStruct(current_time, dt);
    
    // 检查交易时间段
    if (dt.hour < StartHour || dt.hour >= EndHour) {
        return false;
    }
    
    // 避开重要新闻时段 (可根据需要自定义)
    if (AvoidNews) {
        // 避开美国非农就业数据发布时间 (第一个周五 21:30 北京时间)
        if (dt.day_of_week == 5 && dt.hour == 21 && dt.min >= 25 && dt.min <= 35) {
            return false;
        }
        
        // 避开美联储利率决议时间 (通常在北京时间凌晨2:00-3:00)
        if (dt.hour >= 2 && dt.hour <= 3) {
            return false;
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 多单趋势过滤检查
//+------------------------------------------------------------------+
bool CheckLongTrendFilter(const double &ma60[], const double &ma90[], const double &ma180[], const double &close[]) {
    bool trend_ok = true;
    
    // MA60走平过滤：MA60走平不久有行情
    if (UseMA60Filter) {
        double ma60_slope = (ma60[1] - ma60[4]) / 3.0; // 3周期斜率
        if (MathAbs(ma60_slope) > SymbolInfoDouble(Symbol(), SYMBOL_POINT) * 50) {
            trend_ok = false; // MA60斜率过大，不够平缓
        }
    }
    
    // MA90季度线过滤：价格由下往上突破季度线
    if (UseMA90Filter && trend_ok) {
        if (close[1] <= ma90[1]) {
            trend_ok = false; // 价格未突破季度线
        }
    }
    
    // MA180牛熊分界线过滤：价格突破站稳
    if (UseMA180Filter && trend_ok) {
        if (close[1] <= ma180[1] || close[2] <= ma180[2]) {
            trend_ok = false; // 价格未站稳牛熊线
        }
    }
    
    return trend_ok;
}

//+------------------------------------------------------------------+
//| 空单趋势过滤检查
//+------------------------------------------------------------------+
bool CheckShortTrendFilter(const double &ma60[], const double &ma90[], const double &ma180[], const double &close[]) {
    bool trend_ok = true;
    
    // MA60走平过滤：MA60走平不久有行情
    if (UseMA60Filter) {
        double ma60_slope = (ma60[1] - ma60[4]) / 3.0; // 3周期斜率
        if (MathAbs(ma60_slope) > SymbolInfoDouble(Symbol(), SYMBOL_POINT) * 50) {
            trend_ok = false; // MA60斜率过大，不够平缓
        }
    }
    
    // MA90季度线过滤：价格由上往下跌破季度线
    if (UseMA90Filter && trend_ok) {
        if (close[1] >= ma90[1]) {
            trend_ok = false; // 价格未跌破季度线
        }
    }
    
    // MA180牛熊分界线过滤：价格跌破站稳
    if (UseMA180Filter && trend_ok) {
        if (close[1] >= ma180[1] || close[2] >= ma180[2]) {
            trend_ok = false; // 价格未站稳熊市线下方
        }
    }
    
    return trend_ok;
}

//+------------------------------------------------------------------+
//| 检查是否有待执行的Buy Stop单
//+------------------------------------------------------------------+
bool HasPendingBuyStop() {
    for (int i = 0; i < OrdersTotal(); i++) {
        ulong ticket = OrderGetTicket(i);
        if (ticket <= 0) continue;
        
        if (OrderSelect(ticket) && 
            OrderGetString(ORDER_SYMBOL) == Symbol() && 
            OrderGetInteger(ORDER_MAGIC) == MagicNumber &&
            OrderGetInteger(ORDER_TYPE) == ORDER_TYPE_BUY_STOP) {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 检查是否有待执行的Sell Stop单
//+------------------------------------------------------------------+
bool HasPendingSellStop() {
    for (int i = 0; i < OrdersTotal(); i++) {
        ulong ticket = OrderGetTicket(i);
        if (ticket <= 0) continue;
        
        if (OrderSelect(ticket) && 
            OrderGetString(ORDER_SYMBOL) == Symbol() && 
            OrderGetInteger(ORDER_MAGIC) == MagicNumber &&
            OrderGetInteger(ORDER_TYPE) == ORDER_TYPE_SELL_STOP) {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 获取20均线对应K线的高点
//+------------------------------------------------------------------+
double GetMA20BarHigh() {
    double high[];
    ArraySetAsSeries(high, true);
    
    // 获取足够的历史数据
    if (CopyHigh(Symbol(), PERIOD_CURRENT, 0, MA20_Period + 10, high) < MA20_Period + 10) {
        return 0;
    }
    
    // 找到20周期内的最高点作为20均线K的高点
    double max_high = high[1];
    for (int i = 2; i <= MA20_Period; i++) {
        if (high[i] > max_high) {
            max_high = high[i];
        }
    }
    
    return max_high;
}

//+------------------------------------------------------------------+
//| 获取20均线对应K线的低点
//+------------------------------------------------------------------+
double GetMA20BarLow() {
    double low[];
    ArraySetAsSeries(low, true);
    
    // 获取足够的历史数据
    if (CopyLow(Symbol(), PERIOD_CURRENT, 0, MA20_Period + 10, low) < MA20_Period + 10) {
        return 999999;
    }
    
    // 找到20周期内的最低点作为20均线K的低点
    double min_low = low[1];
    for (int i = 2; i <= MA20_Period; i++) {
        if (low[i] < min_low) {
            min_low = low[i];
        }
    }
    
    return min_low;
}

//+------------------------------------------------------------------+
//| 获取10均线对应K线的高点
//+------------------------------------------------------------------+
double GetMA10BarHigh() {
    double high[];
    ArraySetAsSeries(high, true);
    
    // 获取足够的历史数据
    if (CopyHigh(Symbol(), PERIOD_CURRENT, 0, MA10_Period + 10, high) < MA10_Period + 10) {
        return 0;
    }
    
    // 找到10周期内的最高点作为10均线K的高点
    double max_high = high[1];
    for (int i = 2; i <= MA10_Period; i++) {
        if (high[i] > max_high) {
            max_high = high[i];
        }
    }
    
    return max_high;
}

//+------------------------------------------------------------------+
//| 获取10均线对应K线的低点
//+------------------------------------------------------------------+
double GetMA10BarLow() {
    double low[];
    ArraySetAsSeries(low, true);
    
    // 获取足够的历史数据
    if (CopyLow(Symbol(), PERIOD_CURRENT, 0, MA10_Period + 10, low) < MA10_Period + 10) {
        return 999999;
    }
    
    // 找到10周期内的最低点作为10均线K的低点
    double min_low = low[1];
    for (int i = 2; i <= MA10_Period; i++) {
        if (low[i] < min_low) {
            min_low = low[i];
        }
    }
    
    return min_low;
}

//+------------------------------------------------------------------+
//| 计算手数 - 基于1%风险管理原则
//+------------------------------------------------------------------+
double CalculateLotSize(double entry_price, double stop_loss) {
    if (!UseMoneyManagement) {
        return NormalizeLots(FixedLotSize);
    }
    
    // 获取账户余额（实现"赢冲缩"策略）
    double balance = account.Balance();
    double risk_amount = balance * RiskPercent / 100.0;  // 固定1%风险
    double price_diff = MathAbs(entry_price - stop_loss); // 止损距离
    
    if (price_diff <= 0) {
        Print("❌ 错误: 止损距离无效 entry=", entry_price, " stop=", stop_loss);
        return 0;
    }
    
    // 计算每点价值
    double tick_value = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    
    if (tick_size <= 0 || tick_value <= 0) {
        Print("❌ 错误: 无效的品种参数");
        return 0;
    }
    
    // 每点价值 = 每跳价值 * (点大小 / 跳大小)
    double point_value = 0.0;
    if (tick_size > 0) {
        point_value = tick_value * (point / tick_size);
    }
    
    // 计算手数: 风险金额 / (止损点数 * 每点价值)
    double stop_points = 0.0;
    if (point > 0) {
        stop_points = price_diff / point;
    }
    
    double lot_size = 0.0;
    if (stop_points > 0 && point_value > 0) {
        lot_size = risk_amount / (stop_points * point_value);
    }
    
    // 限制最大风险
    double max_risk_amount = balance * MaxRiskPerTrade / 100.0;
    double max_lot_size = 0.0;
    if (stop_points > 0 && point_value > 0) {
        max_lot_size = max_risk_amount / (stop_points * point_value);
    }
    
    if (lot_size > max_lot_size) {
        lot_size = max_lot_size;
        Print("⚠️ 警告: 手数被限制到最大风险 ", MaxRiskPerTrade, "%");
    }
    
    double final_lot_size = NormalizeLots(lot_size);
    
    // 打印详细计算信息
    Print("💰 仓位计算: 余额=", balance, " 风险=", risk_amount, 
          " 止损点数=", (int)stop_points, " 手数=", final_lot_size);
    
    return final_lot_size;
}

//+------------------------------------------------------------------+
//| 标准化手数
//+------------------------------------------------------------------+
double NormalizeLots(double lots) {
    double min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);
    
    if (lots < min_lot) return 0;
    if (lots > max_lot) lots = max_lot;
    
    if (lot_step > 0) {
        return NormalizeDouble(MathRound(lots / lot_step) * lot_step, 2);
    } else {
        return NormalizeDouble(lots, 2);
    }
}

//+------------------------------------------------------------------+
//| 获取指标数据
//+------------------------------------------------------------------+
bool GetIndicatorData(double &ma20[], double &ma10[], double &ma60[], double &ma90[], double &ma180[], double &atr[]) {
    ArraySetAsSeries(ma20, true);
    ArraySetAsSeries(ma10, true);
    ArraySetAsSeries(ma60, true);
    ArraySetAsSeries(ma90, true);
    ArraySetAsSeries(ma180, true);
    ArraySetAsSeries(atr, true);
    
    if (CopyBuffer(ma20_handle, 0, 0, 5, ma20) < 5) return false;
    if (CopyBuffer(ma10_handle, 0, 0, 3, ma10) < 3) return false;
    if (CopyBuffer(ma60_handle, 0, 0, 5, ma60) < 5) return false;
    if (CopyBuffer(ma90_handle, 0, 0, 3, ma90) < 3) return false;
    if (CopyBuffer(ma180_handle, 0, 0, 3, ma180) < 3) return false;
    if (CopyBuffer(atr_handle, 0, 0, 3, atr) < 3) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| 获取价格数据
//+------------------------------------------------------------------+
bool GetPriceData(double &high[], double &low[], double &close[]) {
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    
    if (CopyHigh(Symbol(), PERIOD_CURRENT, 0, 3, high) < 3) return false;
    if (CopyLow(Symbol(), PERIOD_CURRENT, 0, 3, low) < 3) return false;
    if (CopyClose(Symbol(), PERIOD_CURRENT, 0, 3, close) < 3) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| 检查是否有新K线
//+------------------------------------------------------------------+
bool IsNewBar() {
    datetime current_time = iTime(Symbol(), PERIOD_CURRENT, 0);
    if (current_time != last_bar_time) {
        last_bar_time = current_time;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 检查是否有多头持仓
//+------------------------------------------------------------------+
bool HasLongPosition() {
    for (int i = 0; i < PositionsTotal(); i++) {
        if (position.SelectByIndex(i) && 
            position.Symbol() == Symbol() && 
            position.Magic() == MagicNumber && 
            position.PositionType() == POSITION_TYPE_BUY) {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 检查是否有空头持仓
//+------------------------------------------------------------------+
bool HasShortPosition() {
    for (int i = 0; i < PositionsTotal(); i++) {
        if (position.SelectByIndex(i) && 
            position.Symbol() == Symbol() && 
            position.Magic() == MagicNumber && 
            position.PositionType() == POSITION_TYPE_SELL) {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 专家顾问信息面板
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam) {
    if (id == CHARTEVENT_CHART_CHANGE) {
        ShowInfoPanel();
    }
}

//+------------------------------------------------------------------+
//| 显示信息面板
//+------------------------------------------------------------------+
void ShowInfoPanel() {
    string trade_mode = "";
    switch(TradeDirection) {
        case TRADE_BOTH: trade_mode = "多空都做"; break;
        case TRADE_LONG_ONLY: trade_mode = "只做多"; break;
        case TRADE_SHORT_ONLY: trade_mode = "只做空"; break;
    }
    
    // 获取当前均线数据用于显示
    double ma60[], ma90[], ma180[], close[];
    ArraySetAsSeries(ma60, true);
    ArraySetAsSeries(ma90, true);
    ArraySetAsSeries(ma180, true);
    ArraySetAsSeries(close, true);
    
    string trend_status = "未知";
    if (CopyBuffer(ma60_handle, 0, 0, 5, ma60) >= 5 && 
        CopyBuffer(ma90_handle, 0, 0, 2, ma90) >= 2 && 
        CopyBuffer(ma180_handle, 0, 0, 2, ma180) >= 2 &&
        CopyClose(Symbol(), PERIOD_CURRENT, 0, 2, close) >= 2) {
        
        // 分析趋势状态
        double ma60_slope = (ma60[1] - ma60[4]) / 3.0;
        bool ma60_flat = MathAbs(ma60_slope) <= SymbolInfoDouble(Symbol(), SYMBOL_POINT) * 50;
        bool above_ma90 = close[1] > ma90[1];
        bool above_ma180 = close[1] > ma180[1];
        
        if (above_ma180 && above_ma90 && ma60_flat) {
            trend_status = "🟢 强势上涨";
        } else if (above_ma180 && above_ma90) {
            trend_status = "🟡 上涨趋势";
        } else if (!above_ma180 && !above_ma90 && ma60_flat) {
            trend_status = "🔴 强势下跌";
        } else if (!above_ma180 && !above_ma90) {
            trend_status = "🟡 下跌趋势";
        } else {
            trend_status = "⚪ 震荡整理";
        }
    }
    
    string info = "🎯 中长线趋势跟踪系统\n";
    info += "━━━━━━━━━━━━━━━━━━━━\n";
    info += "📊 品种: " + Symbol() + "\n";
    info += "💰 账户余额: " + DoubleToString(account.Balance(), 2) + "\n";
    info += "⚡ 风险比例: " + DoubleToString(RiskPercent, 1) + "%\n";
    info += "🎯 交易方向: " + trade_mode + "\n";
    info += "📈 趋势状态: " + trend_status + "\n";
    info += "🔍 趋势过滤: " + (UseTrendFilter ? "启用" : "关闭") + "\n";
    info += "━━━━━━━━━━━━━━━━━━━━\n";
    
    // 根据交易方向显示相应信号
    if (TradeDirection != TRADE_SHORT_ONLY) {
        info += "🟢 多单信号: " + (long_signal_active ? "激活" : "等待") + "\n";
    }
    if (TradeDirection != TRADE_LONG_ONLY) {
        info += "🔴 空单信号: " + (short_signal_active ? "激活" : "等待") + "\n";
    }
    
    info += "📈 持仓数量: " + IntegerToString(PositionsTotal()) + "\n";
    info += "📋 挂单数量: " + IntegerToString(OrdersTotal()) + "\n";
    
    // 显示时间过滤状态
    if (UseTimeFilter) {
        string time_status = IsTimeToTrade() ? "✅ 交易时间" : "❌ 非交易时间";
        info += "⏰ 时间过滤: " + time_status + "\n";
    }
    
    info += "━━━━━━━━━━━━━━━━━━━━\n";
    
    // 显示交易统计
    UpdateTradeStatistics(); // 更新统计数据
    if (total_trades > 0) {
        double win_rate = 0.0;
        if (total_trades > 0) {
            win_rate = (double)winning_trades / total_trades * 100.0;
        }
        info += "📊 交易统计:\n";
        info += "   总交易: " + IntegerToString(total_trades) + " 笔\n";
        info += "   胜率: " + DoubleToString(win_rate, 1) + "%\n";
        info += "   总盈亏: " + DoubleToString(total_profit, 2) + "\n";
        info += "   最大盈利: " + DoubleToString(max_profit, 2) + "\n";
        info += "   最大亏损: " + DoubleToString(max_loss, 2) + "\n";
        info += "   最大回撤: " + DoubleToString(max_drawdown, 2) + "\n";
        info += "━━━━━━━━━━━━━━━━━━━━\n";
    }
    
    info += "⏰ 更新时间: " + TimeToString(TimeCurrent(), TIME_MINUTES);
    
    Comment(info);
}

//+------------------------------------------------------------------+
//| 更新交易统计
//+------------------------------------------------------------------+
void UpdateTradeStatistics() {
    // 更新峰值余额和最大回撤
    double current_balance = account.Balance();
    if (current_balance > peak_balance) {
        peak_balance = current_balance;
    }
    
    double current_drawdown = peak_balance - current_balance;
    if (current_drawdown > max_drawdown) {
        max_drawdown = current_drawdown;
    }
    
    // 统计历史交易
    HistorySelect(0, TimeCurrent());
    int total_deals = HistoryDealsTotal();
    
    total_trades = 0;
    winning_trades = 0;
    losing_trades = 0;
    total_profit = 0.0;
    max_profit = 0.0;
    max_loss = 0.0;
    
    for (int i = 0; i < total_deals; i++) {
        ulong deal_ticket = HistoryDealGetTicket(i);
        if (deal_ticket > 0) {
            if (HistoryDealGetString(deal_ticket, DEAL_SYMBOL) == Symbol() &&
                HistoryDealGetInteger(deal_ticket, DEAL_MAGIC) == MagicNumber &&
                HistoryDealGetInteger(deal_ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT) {
                
                double profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
                total_profit += profit;
                total_trades++;
                
                if (profit > 0) {
                    winning_trades++;
                    if (profit > max_profit) max_profit = profit;
                } else if (profit < 0) {
                    losing_trades++;
                    if (profit < max_loss) max_loss = profit;
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 交易事件处理
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result) {
    // 处理订单执行事件
    if (trans.type == TRADE_TRANSACTION_DEAL_ADD) {
        if (trans.symbol == Symbol()) {
            string deal_comment = "";
            if (HistoryDealSelect(trans.deal)) {
                deal_comment = HistoryDealGetString(trans.deal, DEAL_COMMENT);
            }
            
            if (StringFind(deal_comment, "Long_BuyStop") >= 0) {
                Print("✅ 多单挂单已执行: 票号=", trans.deal, " 价格=", trans.price);
            }
            else if (StringFind(deal_comment, "Short_SellStop") >= 0) {
                Print("✅ 空单挂单已执行: 票号=", trans.deal, " 价格=", trans.price);
            }
            
            // 更新交易统计
            UpdateTradeStatistics();
        }
    }
}
