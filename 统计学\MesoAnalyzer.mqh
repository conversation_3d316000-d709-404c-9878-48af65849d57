//+------------------------------------------------------------------+
//|                                               MesoAnalyzer.mqh |
//|                                  中观战术分析模块                |
//|                                  时段波动率分析                  |
//+------------------------------------------------------------------+
#property copyright "Professional Volatility Dashboard"
#property link      ""

#include "Defines.mqh"
#include "TimeUtils.mqh"

//+------------------------------------------------------------------+
//| 中观分析器类                                                     |
//+------------------------------------------------------------------+
class CMesoAnalyzer
{
private:
    SMesoConfig       m_config;
    SMesoData         m_cachedData;
    datetime          m_lastCalculateTime;
    
    // 计算指定时段的波动率
    double            CalculateBlockVolatility(int startHour, int endHour, int historyDays);
    
    // 获取当前时段信息
    bool              GetCurrentTimeBlock(int &startHour, int &endHour);
    
    // 获取前序时段信息
    bool              GetPreviousTimeBlock(int currentStart, int currentEnd, int &prevStart, int &prevEnd);
    
public:
    CMesoAnalyzer();
    ~CMesoAnalyzer();
    
    // 初始化配置
    bool              Initialize(const SMesoConfig &config);
    
    // 执行中观分析计算
    bool              Calculate(SMesoData &data);
    
    // 获取缓存的数据
    bool              GetCachedData(SMesoData &data);
};

//+------------------------------------------------------------------+
//| 构造函数                                                         |
//+------------------------------------------------------------------+
CMesoAnalyzer::CMesoAnalyzer()
{
    m_lastCalculateTime = 0;
    ZeroMemory(m_cachedData);
}

//+------------------------------------------------------------------+
//| 析构函数                                                         |
//+------------------------------------------------------------------+
CMesoAnalyzer::~CMesoAnalyzer()
{
}

//+------------------------------------------------------------------+
//| 初始化配置                                                       |
//+------------------------------------------------------------------+
bool CMesoAnalyzer::Initialize(const SMesoConfig &config)
{
    if(config.historyDays <= 0 || config.blockHours <= 0)
    {
        Print("中观分析器：无效的配置参数");
        return false;
    }
    
    if(config.blockHours > 24)
    {
        Print("中观分析器：时间区块不能超过24小时");
        return false;
    }
    
    m_config = config;
    return true;
}

//+------------------------------------------------------------------+
//| 执行中观分析计算                                                 |
//+------------------------------------------------------------------+
bool CMesoAnalyzer::Calculate(SMesoData &data)
{
    // 检查是否需要重新计算（每小时更新一次）
    datetime currentHour = iTime(_Symbol, PERIOD_H1, 0);
    if(m_lastCalculateTime == currentHour && m_cachedData.isValid)
    {
        data = m_cachedData;
        return true;
    }
    
    // 获取当前时段信息
    int currentStart, currentEnd;
    if(!GetCurrentTimeBlock(currentStart, currentEnd))
    {
        Print("中观分析器：无法获取当前时段信息");
        return false;
    }
    
    // 获取前序时段信息
    int prevStart, prevEnd;
    if(!GetPreviousTimeBlock(currentStart, currentEnd, prevStart, prevEnd))
    {
        Print("中观分析器：无法获取前序时段信息");
        return false;
    }
    
    // 计算当前时段波动率
    double currentBlockVolatility = CalculateBlockVolatility(currentStart, currentEnd, m_config.historyDays);
    
    // 计算前序时段波动率
    double previousBlockVolatility = CalculateBlockVolatility(prevStart, prevEnd, m_config.historyDays);
    
    if(currentBlockVolatility <= 0 || previousBlockVolatility <= 0)
    {
        Print("中观分析器：波动率计算失败");
        return false;
    }
    
    // 填充数据结构
    data.currentBlockVolatility = currentBlockVolatility;
    data.previousBlockVolatility = previousBlockVolatility;
    data.currentBlockStart = currentStart;
    data.currentBlockEnd = currentEnd;
    data.previousBlockStart = prevStart;
    data.previousBlockEnd = prevEnd;
    data.isValid = true;
    data.updateTime = TimeCurrent();
    
    // 缓存数据
    m_cachedData = data;
    m_lastCalculateTime = currentHour;
    
    return true;
}

//+------------------------------------------------------------------+
//| 获取缓存的数据                                                   |
//+------------------------------------------------------------------+
bool CMesoAnalyzer::GetCachedData(SMesoData &data)
{
    if(!m_cachedData.isValid)
        return false;
        
    data = m_cachedData;
    return true;
}

//+------------------------------------------------------------------+
//| 计算指定时段的波动率                                             |
//+------------------------------------------------------------------+
double CMesoAnalyzer::CalculateBlockVolatility(int startHour, int endHour, int historyDays)
{
    if(startHour < 0 || startHour > 23 || endHour < 0 || endHour > 23 || historyDays <= 0)
        return 0;
    
    double totalRange = 0;
    int validDays = 0;
    
    // 遍历历史天数
    for(int day = 1; day <= historyDays; day++)
    {
        datetime dayStart = iTime(_Symbol, PERIOD_D1, day);
        if(dayStart == 0)
            continue;
        
        double dayHigh = 0, dayLow = DBL_MAX;
        bool hasData = false;
        
        // 在指定时段内查找最高最低价
        for(int hour = startHour; hour != (endHour + 1) % 24; hour = (hour + 1) % 24)
        {
            datetime hourTime = dayStart + hour * 3600;
            int barIndex = iBarShift(_Symbol, _Period, hourTime);
            
            if(barIndex >= 0)
            {
                double high = iHigh(_Symbol, _Period, barIndex);
                double low = iLow(_Symbol, _Period, barIndex);
                
                if(high > 0 && low > 0)
                {
                    if(high > dayHigh)
                        dayHigh = high;
                    if(low < dayLow)
                        dayLow = low;
                    hasData = true;
                }
            }
            
            // 如果是跨日时段，需要特殊处理
            if(startHour > endHour && hour == endHour)
                break;
        }
        
        if(hasData && dayHigh > dayLow)
        {
            totalRange += (dayHigh - dayLow);
            validDays++;
        }
    }
    
    if(validDays == 0)
        return 0;
    
    // 返回平均波幅（转换为点数）
    double avgRange = totalRange / validDays;
    double pointValue = _Point;
    if(_Digits == 5 || _Digits == 3)
        pointValue *= 10;
    
    return avgRange / pointValue;
}

//+------------------------------------------------------------------+
//| 获取当前时段信息                                                 |
//+------------------------------------------------------------------+
bool CMesoAnalyzer::GetCurrentTimeBlock(int &startHour, int &endHour)
{
    // 获取当前北京时间
    datetime beijingTime = Time_GetAccurateBeijingTime();
    MqlDateTime dt;
    TimeToStruct(beijingTime, dt);
    
    int currentHour = dt.hour;
    
    // 根据配置的区块小时数计算当前时段
    int blockSize = m_config.blockHours;
    int blockIndex = currentHour / blockSize;
    
    startHour = blockIndex * blockSize;
    endHour = startHour + blockSize - 1;
    
    // 确保小时数在有效范围内
    if(endHour >= 24)
        endHour = 23;
    
    return true;
}

//+------------------------------------------------------------------+
//| 获取前序时段信息                                                 |
//+------------------------------------------------------------------+
bool CMesoAnalyzer::GetPreviousTimeBlock(int currentStart, int currentEnd, int &prevStart, int &prevEnd)
{
    int blockSize = m_config.blockHours;
    
    prevStart = currentStart - blockSize;
    prevEnd = currentEnd - blockSize;
    
    // 处理跨日情况
    if(prevStart < 0)
    {
        prevStart += 24;
        prevEnd += 24;
        if(prevEnd >= 24)
            prevEnd = 23;
    }
    
    return true;
}