//+------------------------------------------------------------------+
//|                                           VolatilityAnalyzer.mqh |
//|                                  波动率分析核心模块              |
//|                                  实现分析器函数                  |
//+------------------------------------------------------------------+
#property copyright "Professional Volatility Dashboard"
#property link      ""

#ifndef VOLATILITY_ANALYZER_MQH
#define VOLATILITY_ANALYZER_MQH

#include "Defines.mqh"
#include "TimeUtils.mqh"

//+------------------------------------------------------------------+
//| 获取当前小时已走波幅（点数）                                     |
//+------------------------------------------------------------------+
double Analyzer_GetCurrentHourRangePips(const string symbol)
{
    // 获取当前小时的开始时间
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    dt.min = 0;
    dt.sec = 0;
    datetime hour_start = StructToTime(dt);
    
    // 查找当前小时开始的K线索引
    int start_bar = iBarShift(symbol, PERIOD_M1, hour_start);
    if(start_bar < 0) return 0;
    
    // 获取当前小时内的最高价和最低价
    double hour_high = iHigh(symbol, PERIOD_M1, 0);
    double hour_low = iLow(symbol, PERIOD_M1, 0);
    
    for(int i = 1; i <= start_bar; i++)
    {
        double high = iHigh(symbol, PERIOD_M1, i);
        double low = iLow(symbol, PERIOD_M1, i);
        
        if(high > hour_high) hour_high = high;
        if(low < hour_low) hour_low = low;
    }
    
    // 转换为点数
    double range = hour_high - hour_low;
    double point_value = SymbolInfoDouble(symbol, SYMBOL_POINT);
    if(SymbolInfoInteger(symbol, SYMBOL_DIGITS) == 5 || 
       SymbolInfoInteger(symbol, SYMBOL_DIGITS) == 3)
        point_value *= 10;
    
    return range / point_value;
}

//+------------------------------------------------------------------+
//| 获取当前小时的历史平均波幅                                       |
//+------------------------------------------------------------------+
double Analyzer_GetCurrentHourAverageRange(const string symbol)
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int current_hour = dt.hour;
    
    double total_range = 0;
    int valid_days = 0;
    int history_days = 30; // 使用30天历史数据
    
    // 遍历历史数据
    for(int day = 1; day <= history_days; day++)
    {
        datetime day_start = iTime(symbol, PERIOD_D1, day);
        if(day_start == 0) continue;
        
        // 计算该天同一小时的波幅
        datetime hour_time = day_start + current_hour * 3600;
        int hour_bar = iBarShift(symbol, PERIOD_H1, hour_time);
        
        if(hour_bar >= 0)
        {
            double high = iHigh(symbol, PERIOD_H1, hour_bar);
            double low = iLow(symbol, PERIOD_H1, hour_bar);
            
            if(high > 0 && low > 0)
            {
                double range = high - low;
                double point_value = SymbolInfoDouble(symbol, SYMBOL_POINT);
                if(SymbolInfoInteger(symbol, SYMBOL_DIGITS) == 5 || 
                   SymbolInfoInteger(symbol, SYMBOL_DIGITS) == 3)
                    point_value *= 10;
                
                total_range += range / point_value;
                valid_days++;
            }
        }
    }
    
    return valid_days > 0 ? total_range / valid_days : 0;
}

//+------------------------------------------------------------------+
//| 计算日均波幅ADR                                                  |
//+------------------------------------------------------------------+
double Analyzer_CalculateADR(const string symbol, int period)
{
    if(period <= 0) return 0;
    
    double atr_value = iATR(symbol, PERIOD_D1, period, 0);
    if(atr_value == EMPTY_VALUE || atr_value <= 0) return 0;
    
    // 转换为点数
    double point_value = SymbolInfoDouble(symbol, SYMBOL_POINT);
    if(SymbolInfoInteger(symbol, SYMBOL_DIGITS) == 5 || 
       SymbolInfoInteger(symbol, SYMBOL_DIGITS) == 3)
        point_value *= 10;
    
    return atr_value / point_value;
}

//+------------------------------------------------------------------+
//| 获取今日已走波幅                                                 |
//+------------------------------------------------------------------+
double Analyzer_GetTodayRangePips(const string symbol)
{
    // 获取今日开盘时间
    datetime today_start = iTime(symbol, PERIOD_D1, 0);
    if(today_start == 0) return 0;
    
    // 查找今日开始的K线索引
    int start_bar = iBarShift(symbol, _Period, today_start);
    if(start_bar < 0) return 0;
    
    // 获取今日最高价和最低价
    double today_high = iHigh(symbol, _Period, 0);
    double today_low = iLow(symbol, _Period, 0);
    
    for(int i = 1; i <= start_bar; i++)
    {
        double high = iHigh(symbol, _Period, i);
        double low = iLow(symbol, _Period, i);
        
        if(high > today_high) today_high = high;
        if(low < today_low) today_low = low;
    }
    
    // 转换为点数
    double range = today_high - today_low;
    double point_value = SymbolInfoDouble(symbol, SYMBOL_POINT);
    if(SymbolInfoInteger(symbol, SYMBOL_DIGITS) == 5 || 
       SymbolInfoInteger(symbol, SYMBOL_DIGITS) == 3)
        point_value *= 10;
    
    return range / point_value;
}

//+------------------------------------------------------------------+
//| 获取当前交易时段                                                 |
//+------------------------------------------------------------------+
ENUM_TRADING_SESSION Analyzer_GetCurrentSession()
{
    MqlDateTime dt;
    TimeToStruct(TimeGMT(), dt);
    int gmt_hour = dt.hour;
    
    // 亚洲时段：GMT 0:00-9:00
    if(gmt_hour >= 0 && gmt_hour < 9)
        return SESSION_ASIA;
    
    // 欧洲时段：GMT 9:00-17:00
    if(gmt_hour >= 9 && gmt_hour < 17)
        return SESSION_EUROPE;
    
    // 欧美交叉时段：GMT 17:00-22:00
    if(gmt_hour >= 17 && gmt_hour < 22)
        return SESSION_US_EUROPE_CROSS;
    
    // 美盘尾盘：GMT 22:00-24:00
    if(gmt_hour >= 22)
        return SESSION_US_NIGHT;
    
    // 其他时间
    return SESSION_NONE;
}

//+------------------------------------------------------------------+
//| 获取交易时段名称                                                 |
//+------------------------------------------------------------------+
string Analyzer_GetSessionName(ENUM_TRADING_SESSION session)
{
    switch(session)
    {
        case SESSION_ASIA: return "亚洲时段";
        case SESSION_EUROPE: return "欧洲时段";
        case SESSION_US_EUROPE_CROSS: return "欧美交叉";
        case SESSION_US_NIGHT: return "美盘尾盘";
        default: return "休市时段";
    }
}

//+------------------------------------------------------------------+
//| 计算时段历史平均波幅                                             |
//+------------------------------------------------------------------+
double Analyzer_GetSessionAverageRange(const string symbol, ENUM_TRADING_SESSION session, int history_days = 20)
{
    if(history_days <= 0) return 0;
    
    double total_range = 0;
    int valid_days = 0;
    
    for(int day = 1; day <= history_days; day++)
    {
        datetime day_start = iTime(symbol, PERIOD_D1, day);
        if(day_start == 0) continue;
        
        // 计算该天该时段的波幅
        double day_session_range = CalculateDaySessionRange(symbol, day_start, session);
        if(day_session_range > 0)
        {
            total_range += day_session_range;
            valid_days++;
        }
    }
    
    return valid_days > 0 ? total_range / valid_days : 0;
}

//+------------------------------------------------------------------+
//| 计算指定日期指定时段的波幅                                       |
//+------------------------------------------------------------------+
double CalculateDaySessionRange(const string symbol, datetime day_start, ENUM_TRADING_SESSION session)
{
    // 根据时段确定GMT小时范围
    int start_hour, end_hour;
    switch(session)
    {
        case SESSION_ASIA:
            start_hour = 0; end_hour = 8;
            break;
        case SESSION_EUROPE:
            start_hour = 9; end_hour = 16;
            break;
        case SESSION_US_EUROPE_CROSS:
            start_hour = 17; end_hour = 21;
            break;
        case SESSION_US_NIGHT:
            start_hour = 22; end_hour = 23;
            break;
        default:
            return 0;
    }
    
    double session_high = 0;
    double session_low = DBL_MAX;
    bool has_data = false;
    
    // 遍历时段内的小时
    for(int hour = start_hour; hour <= end_hour; hour++)
    {
        datetime hour_time = day_start + hour * 3600;
        int hour_bar = iBarShift(symbol, PERIOD_H1, hour_time);
        
        if(hour_bar >= 0)
        {
            double high = iHigh(symbol, PERIOD_H1, hour_bar);
            double low = iLow(symbol, PERIOD_H1, hour_bar);
            
            if(high > 0 && low > 0)
            {
                if(session_high == 0 || high > session_high)
                    session_high = high;
                if(low < session_low)
                    session_low = low;
                has_data = true;
            }
        }
    }
    
    if(!has_data || session_high <= session_low)
        return 0;
    
    // 转换为点数
    double range = session_high - session_low;
    double point_value = SymbolInfoDouble(symbol, SYMBOL_POINT);
    if(SymbolInfoInteger(symbol, SYMBOL_DIGITS) == 5 || 
       SymbolInfoInteger(symbol, SYMBOL_DIGITS) == 3)
        point_value *= 10;
    
    return range / point_value;
}

//+------------------------------------------------------------------+
//| 获取当前时段已走波幅                                             |
//+------------------------------------------------------------------+
double Analyzer_GetCurrentSessionRangePips(const string symbol)
{
    ENUM_TRADING_SESSION current_session = Analyzer_GetCurrentSession();
    if(current_session == SESSION_NONE)
        return 0;
    
    // 获取今日开始时间
    datetime today_start = iTime(symbol, PERIOD_D1, 0);
    if(today_start == 0) return 0;
    
    return CalculateDaySessionRange(symbol, today_start, current_session);
}

#endif // VOLATILITY_ANALYZER_MQH