//+------------------------------------------------------------------+
//|                                              MacroAnalyzer.mqh |
//|                                  宏观战略分析模块                |
//|                                  日均波幅ADR计算                 |
//+------------------------------------------------------------------+
#property copyright "Professional Volatility Dashboard"
#property link      ""

#include "Defines.mqh"

//+------------------------------------------------------------------+
//| 宏观分析器类                                                     |
//+------------------------------------------------------------------+
class CMacroAnalyzer
{
private:
    SMacroConfig      m_config;
    SMacroData        m_cachedData;
    datetime          m_lastCalculateTime;
    
    // 计算ADR值
    double            CalculateADR(int period);
    
    // 获取当日波幅数据
    bool              GetTodayRange(double &todayHigh, double &todayLow);
    
public:
    CMacroAnalyzer();
    ~CMacroAnalyzer();
    
    // 初始化配置
    bool              Initialize(const SMacroConfig &config);
    
    // 执行宏观分析计算
    bool              Calculate(SMacroData &data);
    
    // 获取缓存的数据
    bool              GetCachedData(SMacroData &data);
};

//+------------------------------------------------------------------+
//| 构造函数                                                         |
//+------------------------------------------------------------------+
CMacroAnalyzer::CMacroAnalyzer()
{
    m_lastCalculateTime = 0;
    ZeroMemory(m_cachedData);
}

//+------------------------------------------------------------------+
//| 析构函数                                                         |
//+------------------------------------------------------------------+
CMacroAnalyzer::~CMacroAnalyzer()
{
}

//+------------------------------------------------------------------+
//| 初始化配置                                                       |
//+------------------------------------------------------------------+
bool CMacroAnalyzer::Initialize(const SMacroConfig &config)
{
    if(config.slowPeriod <= 0 || config.fastPeriod <= 0)
    {
        Print("宏观分析器：无效的周期参数");
        return false;
    }
    
    if(config.fastPeriod >= config.slowPeriod)
    {
        Print("宏观分析器：快速周期应小于慢速周期");
        return false;
    }
    
    m_config = config;
    return true;
}

//+------------------------------------------------------------------+
//| 执行宏观分析计算                                                 |
//+------------------------------------------------------------------+
bool CMacroAnalyzer::Calculate(SMacroData &data)
{
    // 检查是否需要重新计算（每日更新一次）
    datetime currentDay = iTime(_Symbol, PERIOD_D1, 0);
    if(m_lastCalculateTime == currentDay && m_cachedData.isValid)
    {
        data = m_cachedData;
        return true;
    }
    
    // 计算慢速和快速ADR
    double slowADR = CalculateADR(m_config.slowPeriod);
    double fastADR = CalculateADR(m_config.fastPeriod);
    
    if(slowADR <= 0 || fastADR <= 0)
    {
        Print("宏观分析器：ADR计算失败");
        return false;
    }
    
    // 获取当日波幅数据
    double todayHigh, todayLow;
    if(!GetTodayRange(todayHigh, todayLow))
    {
        Print("宏观分析器：无法获取当日波幅数据");
        return false;
    }
    
    // 计算今日已走波幅
    double todayRange = todayHigh - todayLow;
    
    // 计算剩余空间（确保不为负数）
    double remainingSpace = MathMax(0, fastADR - todayRange);
    
    // 计算完成度百分比
    double completionPercent = (todayRange / fastADR) * 100.0;
    
    // 填充数据结构
    data.slowADR = slowADR;
    data.fastADR = fastADR;
    data.todayRange = todayRange;
    data.remainingSpace = remainingSpace;
    data.completionPercent = completionPercent;
    data.isValid = true;
    data.updateTime = TimeCurrent();
    
    // 缓存数据
    m_cachedData = data;
    m_lastCalculateTime = currentDay;
    
    return true;
}

//+------------------------------------------------------------------+
//| 获取缓存的数据                                                   |
//+------------------------------------------------------------------+
bool CMacroAnalyzer::GetCachedData(SMacroData &data)
{
    if(!m_cachedData.isValid)
        return false;
        
    data = m_cachedData;
    return true;
}

//+------------------------------------------------------------------+
//| 计算ADR值                                                        |
//+------------------------------------------------------------------+
double CMacroAnalyzer::CalculateADR(int period)
{
    if(period <= 0)
        return 0;
    
    // 使用日线时间框架计算ATR
    double atrValue = iATR(_Symbol, PERIOD_D1, period, 1);
    
    if(atrValue == EMPTY_VALUE || atrValue <= 0)
    {
        Print("宏观分析器：ATR计算失败，周期=", period);
        return 0;
    }
    
    // 转换为点数
    double pointValue = _Point;
    if(_Digits == 5 || _Digits == 3)
        pointValue *= 10;
    
    return atrValue / pointValue;
}

//+------------------------------------------------------------------+
//| 获取当日波幅数据                                                 |
//+------------------------------------------------------------------+
bool CMacroAnalyzer::GetTodayRange(double &todayHigh, double &todayLow)
{
    // 获取当日开盘时间
    datetime todayStart = iTime(_Symbol, PERIOD_D1, 0);
    if(todayStart == 0)
        return false;
    
    // 获取当前时间
    datetime currentTime = TimeCurrent();
    
    // 在当前时间框架内查找当日的最高价和最低价
    int startBar = iBarShift(_Symbol, _Period, todayStart);
    int endBar = 0;
    
    if(startBar < 0)
        return false;
    
    todayHigh = iHigh(_Symbol, _Period, endBar);
    todayLow = iLow(_Symbol, _Period, endBar);
    
    // 遍历当日所有K线找到真正的最高最低价
    for(int i = endBar; i <= startBar; i++)
    {
        double high = iHigh(_Symbol, _Period, i);
        double low = iLow(_Symbol, _Period, i);
        
        if(high > todayHigh)
            todayHigh = high;
        if(low < todayLow)
            todayLow = low;
    }
    
    return (todayHigh > 0 && todayLow > 0 && todayHigh >= todayLow);
}