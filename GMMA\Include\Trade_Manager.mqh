//+------------------------------------------------------------------+
//|                                              Trade_Manager.mqh |
//|                                            交易执行和管理模块    |
//+------------------------------------------------------------------+
#property copyright "Trade Manager Module"
#property version   "1.00"

#include <Trade\Trade.mqh>

//+------------------------------------------------------------------+
//| 交易管理器类                                                      |
//+------------------------------------------------------------------+
class CTrade_Manager
{
private:
   //--- 交易对象
   CTrade*           m_trade;                    // 交易对象指针
   
   //--- 参数
   long              m_magic_number;            // 魔法数字
   int               m_atr_period;              // ATR周期
   double            m_atr_multiplier;          // ATR乘数
   int               m_max_spread_points;       // 最大点差
   
   //--- ATR句柄
   int               m_atr_handle;              // ATR指标句柄
   
   //--- 状态标志
   bool              m_initialized;
   
public:
   //--- 构造函数和析构函数
                     CTrade_Manager();
                    ~CTrade_Manager();
   
   //--- 初始化和清理
   bool              Initialize(CTrade* trade_obj, long magic_number, int atr_period, 
                               double atr_multiplier, int max_spread_points);
   void              Cleanup();
   
   //--- 交易执行
   bool              OpenLongPosition(double volume, double stop_loss, double take_profit = 0);
   bool              CloseAllPositions(string reason = "");
   bool              ClosePosition(ulong ticket, string reason = "");
   
   //--- 仓位管理
   bool              HasPosition();
   int               GetPositionCount();
   string            GetPositionInfo();
   double            GetPositionProfit();
   
   //--- 计算方法
   double            CalculateStopLoss(ENUM_ORDER_TYPE order_type);
   bool              CheckSpread();
   
   //--- 订单管理
   bool              ModifyPosition(ulong ticket, double new_sl, double new_tp);
   
private:
   //--- 内部方法
   double            GetATRValue();
   bool              IsValidPrice(double price);
   double            NormalizePrice(double price);
   void              LogTradeResult(bool success, string operation, string details = "");
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
CTrade_Manager::CTrade_Manager()
{
   m_trade = NULL;
   m_magic_number = 0;
   m_atr_period = 14;
   m_atr_multiplier = 1.5;
   m_max_spread_points = 30;
   m_atr_handle = INVALID_HANDLE;
   m_initialized = false;
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
CTrade_Manager::~CTrade_Manager()
{
   Cleanup();
}

//+------------------------------------------------------------------+
//| 初始化交易管理器                                                  |
//+------------------------------------------------------------------+
bool CTrade_Manager::Initialize(CTrade* trade_obj, long magic_number, int atr_period, 
                               double atr_multiplier, int max_spread_points)
{
   Print("初始化交易管理器...");
   
   if(trade_obj == NULL)
   {
      Print("交易对象为空");
      return false;
   }
   
   //--- 保存参数
   m_trade = trade_obj;
   m_magic_number = magic_number;
   m_atr_period = atr_period;
   m_atr_multiplier = atr_multiplier;
   m_max_spread_points = max_spread_points;
   
   //--- 创建ATR指标句柄
   m_atr_handle = iATR(Symbol(), PERIOD_CURRENT, m_atr_period);
   if(m_atr_handle == INVALID_HANDLE)
   {
      Print("ATR指标创建失败");
      return false;
   }
   
   m_initialized = true;
   Print("交易管理器初始化完成，魔法数字: ", m_magic_number);
   
   return true;
}

//+------------------------------------------------------------------+
//| 清理资源                                                          |
//+------------------------------------------------------------------+
void CTrade_Manager::Cleanup()
{
   if(m_atr_handle != INVALID_HANDLE)
   {
      IndicatorRelease(m_atr_handle);
      m_atr_handle = INVALID_HANDLE;
   }
   
   m_initialized = false;
}

//+------------------------------------------------------------------+
//| 开立多头仓位                                                      |
//+------------------------------------------------------------------+
bool CTrade_Manager::OpenLongPosition(double volume, double stop_loss, double take_profit = 0)
{
   if(!m_initialized || m_trade == NULL)
   {
      Print("交易管理器未初始化");
      return false;
   }
   
   //--- 检查点差
   if(!CheckSpread())
   {
      Print("点差过大，取消开仓");
      return false;
   }
   
   //--- 获取当前价格
   double ask_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
   if(ask_price <= 0)
   {
      Print("获取ASK价格失败");
      return false;
   }
   
   //--- 标准化价格
   stop_loss = NormalizePrice(stop_loss);
   if(take_profit > 0)
      take_profit = NormalizePrice(take_profit);
   
   //--- 验证止损价格
   if(!IsValidPrice(stop_loss) || stop_loss >= ask_price)
   {
      Print("止损价格无效: ", stop_loss, ", 当前价格: ", ask_price);
      return false;
   }
   
   //--- 执行开仓
   bool result = m_trade.Buy(volume, Symbol(), ask_price, stop_loss, take_profit, "GMMA Long");
   
   if(result)
   {
      Print("多头开仓成功: 手数=", volume, ", 价格=", ask_price, ", 止损=", stop_loss);
      LogTradeResult(true, "开多仓", StringFormat("手数=%.2f, 价格=%.5f, 止损=%.5f", volume, ask_price, stop_loss));
   }
   else
   {
      Print("多头开仓失败: ", m_trade.ResultRetcodeDescription());
      LogTradeResult(false, "开多仓", m_trade.ResultRetcodeDescription());
   }
   
   return result;
}

//+------------------------------------------------------------------+
//| 平掉所有仓位                                                      |
//+------------------------------------------------------------------+
bool CTrade_Manager::CloseAllPositions(string reason = "")
{
   if(!m_initialized || m_trade == NULL)
      return false;
   
   bool all_closed = true;
   int total_positions = PositionsTotal();
   
   for(int i = total_positions - 1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket == 0)
         continue;
      
      //--- 检查是否是我们的仓位
      if(PositionGetInteger(POSITION_MAGIC) != m_magic_number)
         continue;
      
      //--- 平仓
      if(!ClosePosition(ticket, reason))
      {
         all_closed = false;
      }
   }
   
   if(all_closed && total_positions > 0)
   {
      Print("所有仓位已平仓，原因: ", reason);
   }
   
   return all_closed;
}

//+------------------------------------------------------------------+
//| 平掉指定仓位                                                      |
//+------------------------------------------------------------------+
bool CTrade_Manager::ClosePosition(ulong ticket, string reason = "")
{
   if(!m_initialized || m_trade == NULL)
      return false;
   
   if(!PositionSelectByTicket(ticket))
   {
      Print("选择仓位失败: ", ticket);
      return false;
   }
   
   //--- 检查魔法数字
   if(PositionGetInteger(POSITION_MAGIC) != m_magic_number)
   {
      Print("仓位魔法数字不匹配: ", ticket);
      return false;
   }
   
   //--- 执行平仓
   bool result = m_trade.PositionClose(ticket);
   
   if(result)
   {
      double profit = PositionGetDouble(POSITION_PROFIT);
      Print("仓位平仓成功: ", ticket, ", 盈亏: ", profit, ", 原因: ", reason);
      LogTradeResult(true, "平仓", StringFormat("票号=%d, 盈亏=%.2f, 原因=%s", ticket, profit, reason));
   }
   else
   {
      Print("仓位平仓失败: ", ticket, ", 错误: ", m_trade.ResultRetcodeDescription());
      LogTradeResult(false, "平仓", m_trade.ResultRetcodeDescription());
   }
   
   return result;
}

//+------------------------------------------------------------------+
//| 检查是否有仓位                                                    |
//+------------------------------------------------------------------+
bool CTrade_Manager::HasPosition()
{
   return GetPositionCount() > 0;
}

//+------------------------------------------------------------------+
//| 获取仓位数量                                                      |
//+------------------------------------------------------------------+
int CTrade_Manager::GetPositionCount()
{
   int count = 0;
   int total = PositionsTotal();
   
   for(int i = 0; i < total; i++)
   {
      if(PositionGetTicket(i) == 0)
         continue;
      
      if(PositionGetInteger(POSITION_MAGIC) == m_magic_number)
      {
         count++;
      }
   }
   
   return count;
}

//+------------------------------------------------------------------+
//| 获取仓位信息                                                      |
//+------------------------------------------------------------------+
string CTrade_Manager::GetPositionInfo()
{
   int position_count = GetPositionCount();
   if(position_count == 0)
      return "无仓位";
   
   double total_volume = 0;
   double total_profit = 0;
   string position_type = "";
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetTicket(i) == 0)
         continue;
      
      if(PositionGetInteger(POSITION_MAGIC) != m_magic_number)
         continue;
      
      total_volume += PositionGetDouble(POSITION_VOLUME);
      total_profit += PositionGetDouble(POSITION_PROFIT);
      
      if(position_type == "")
      {
         ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
         position_type = (pos_type == POSITION_TYPE_BUY) ? "多头" : "空头";
      }
   }
   
   return StringFormat("%s %.2f手 盈亏:%.2f", position_type, total_volume, total_profit);
}

//+------------------------------------------------------------------+
//| 获取仓位总盈亏                                                    |
//+------------------------------------------------------------------+
double CTrade_Manager::GetPositionProfit()
{
   double total_profit = 0;
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetTicket(i) == 0)
         continue;
      
      if(PositionGetInteger(POSITION_MAGIC) == m_magic_number)
      {
         total_profit += PositionGetDouble(POSITION_PROFIT);
      }
   }
   
   return total_profit;
}

//+------------------------------------------------------------------+
//| 计算止损价格                                                      |
//+------------------------------------------------------------------+
double CTrade_Manager::CalculateStopLoss(ENUM_ORDER_TYPE order_type)
{
   if(!m_initialized)
      return 0;
   
   double atr_value = GetATRValue();
   if(atr_value <= 0)
      return 0;
   
   double stop_distance = atr_value * m_atr_multiplier;
   double current_price = 0;
   
   if(order_type == ORDER_TYPE_BUY)
   {
      current_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
      return NormalizePrice(current_price - stop_distance);
   }
   else if(order_type == ORDER_TYPE_SELL)
   {
      current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
      return NormalizePrice(current_price + stop_distance);
   }
   
   return 0;
}

//+------------------------------------------------------------------+
//| 检查点差                                                          |
//+------------------------------------------------------------------+
bool CTrade_Manager::CheckSpread()
{
   int current_spread = (int)SymbolInfoInteger(Symbol(), SYMBOL_SPREAD);
   
   if(current_spread > m_max_spread_points)
   {
      Print("当前点差过大: ", current_spread, " > ", m_max_spread_points);
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 修改仓位                                                          |
//+------------------------------------------------------------------+
bool CTrade_Manager::ModifyPosition(ulong ticket, double new_sl, double new_tp)
{
   if(!m_initialized || m_trade == NULL)
      return false;
   
   if(!PositionSelectByTicket(ticket))
      return false;
   
   //--- 标准化价格
   new_sl = NormalizePrice(new_sl);
   new_tp = NormalizePrice(new_tp);
   
   //--- 执行修改
   bool result = m_trade.PositionModify(ticket, new_sl, new_tp);
   
   if(result)
   {
      Print("仓位修改成功: ", ticket, ", 新止损: ", new_sl, ", 新止盈: ", new_tp);
      LogTradeResult(true, "修改仓位", StringFormat("票号=%d, 止损=%.5f, 止盈=%.5f", ticket, new_sl, new_tp));
   }
   else
   {
      Print("仓位修改失败: ", ticket, ", 错误: ", m_trade.ResultRetcodeDescription());
      LogTradeResult(false, "修改仓位", m_trade.ResultRetcodeDescription());
   }
   
   return result;
}

//+------------------------------------------------------------------+
//| 获取ATR值                                                         |
//+------------------------------------------------------------------+
double CTrade_Manager::GetATRValue()
{
   if(m_atr_handle == INVALID_HANDLE)
      return 0;
   
   double atr_buffer[];
   if(CopyBuffer(m_atr_handle, 0, 0, 1, atr_buffer) != 1)
   {
      Print("获取ATR数据失败");
      return 0;
   }
   
   return atr_buffer[0];
}

//+------------------------------------------------------------------+
//| 验证价格有效性                                                    |
//+------------------------------------------------------------------+
bool CTrade_Manager::IsValidPrice(double price)
{
   if(price <= 0)
      return false;
   
   double min_stop_level = SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL) * SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   
   return (MathAbs(price - current_price) >= min_stop_level);
}

//+------------------------------------------------------------------+
//| 标准化价格                                                        |
//+------------------------------------------------------------------+
double CTrade_Manager::NormalizePrice(double price)
{
   double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
   if(tick_size > 0)
   {
      return MathRound(price / tick_size) * tick_size;
   }
   
   int digits = (int)SymbolInfoInteger(Symbol(), SYMBOL_DIGITS);
   return NormalizeDouble(price, digits);
}

//+------------------------------------------------------------------+
//| 记录交易结果                                                      |
//+------------------------------------------------------------------+
void CTrade_Manager::LogTradeResult(bool success, string operation, string details = "")
{
   string status = success ? "成功" : "失败";
   string log_message = StringFormat("[交易管理] %s %s", operation, status);
   
   if(details != "")
   {
      log_message += " - " + details;
   }
   
   Print(log_message);
   
   //--- 可以在这里添加更详细的日志记录，比如写入文件等
}