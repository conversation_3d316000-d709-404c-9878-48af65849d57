//+------------------------------------------------------------------+
//|                                                      统计学.mq5 |
//|                                  专业波动率仪表盘 v2.0          |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Professional Volatility Dashboard"
#property link      ""
#property version   "2.00"
#property indicator_chart_window
#property indicator_plots 0

// 包含必要的头文件
#include "Defines.mqh"
#include "TimeUtils.mqh"
#include "DataCollector.mqh"
#include "MacroAnalyzer.mqh"
#include "MesoAnalyzer.mqh"
#include "MicroAnalyzer.mqh"
#include "PanelManager.mqh"

//--- 通用设置
input string   IndicatorName = "VolatilityDashboard";     // 用于图形对象的唯一名称前缀
input ENUM_BASE_CORNER Corner = CORNER_LEFT_UPPER;        // 面板在图表上的位置
input int      X_Offset = 10;                             // 距离角落的水平偏移量
input int      Y_Offset = 20;                             // 距离角落的垂直偏移量
input color    PanelColor = C'30,30,30';                  // 面板背景颜色
input color    TextColor = C'211,211,211';                // 默认文本颜色
input int      FontSize = 8;                              // 所有文本的字体大小

//--- 层次一：宏观参数
input int      ADR_Slow_Period = 252;                     // 慢速ADR周期（日）
input int      ADR_Fast_Period = 20;                      // 快速ADR周期（日）

//--- 层次二：中观参数
input int      Timeframe_History_Days = 20;               // 用于分时段分析的历史天数
input int      Time_Block_Hours = 3;                      // 每个时间区块的持续小时数

//--- 层次三：微观参数
input int      Micro_Slow_Period = 252;                   // 微观动态的慢速周期（K线数）
input int      Micro_Fast_Period = 20;                    // 微观动态的快速周期（K线数）
input color    Status_Heating_Up_Color = C'0,255,0';      // "升温中"状态的颜色
input color    Status_Cooling_Down_Color = C'255,0,0';    // "降温中"状态的颜色

//--- 全局变量
CDataCollector*   g_dataCollector = NULL;
CMacroAnalyzer*   g_macroAnalyzer = NULL;
CMesoAnalyzer*    g_mesoAnalyzer = NULL;
CMicroAnalyzer*   g_microAnalyzer = NULL;
CPanelManager*    g_panelManager = NULL;

// 缓存变量
static datetime   g_lastUpdateTime = 0;
static datetime   g_lastDayUpdate = 0;
static datetime   g_lastHourUpdate = 0;

//+------------------------------------------------------------------+
//| 自定义指标初始化函数                                             |
//+------------------------------------------------------------------+
int OnInit()
{
    // 创建核心组件实例
    g_dataCollector = new CDataCollector();
    g_macroAnalyzer = new CMacroAnalyzer();
    g_mesoAnalyzer = new CMesoAnalyzer();
    g_microAnalyzer = new CMicroAnalyzer();
    g_panelManager = new CPanelManager();
    
    // 检查实例创建是否成功
    if(!g_dataCollector || !g_macroAnalyzer || !g_mesoAnalyzer || 
       !g_microAnalyzer || !g_panelManager)
    {
        Print("错误：无法创建核心组件实例");
        return INIT_FAILED;
    }
    
    // 初始化面板管理器
    SPanelConfig panelConfig;
    panelConfig.corner = Corner;
    panelConfig.xOffset = X_Offset;
    panelConfig.yOffset = Y_Offset;
    panelConfig.panelColor = PanelColor;
    panelConfig.textColor = TextColor;
    panelConfig.fontSize = FontSize;
    panelConfig.indicatorName = IndicatorName;
    
    if(!g_panelManager.Initialize(panelConfig))
    {
        Print("错误：面板管理器初始化失败");
        return INIT_FAILED;
    }
    
    // 初始化分析器参数
    SMacroConfig macroConfig;
    macroConfig.slowPeriod = ADR_Slow_Period;
    macroConfig.fastPeriod = ADR_Fast_Period;
    g_macroAnalyzer.Initialize(macroConfig);
    
    SMesoConfig mesoConfig;
    mesoConfig.historyDays = Timeframe_History_Days;
    mesoConfig.blockHours = Time_Block_Hours;
    g_mesoAnalyzer.Initialize(mesoConfig);
    
    SMicroConfig microConfig;
    microConfig.slowPeriod = Micro_Slow_Period;
    microConfig.fastPeriod = Micro_Fast_Period;
    microConfig.heatingColor = Status_Heating_Up_Color;
    microConfig.coolingColor = Status_Cooling_Down_Color;
    microConfig.normalColor = TextColor;
    g_microAnalyzer.Initialize(microConfig);
    
    Print("专业波动率仪表盘初始化成功");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| 自定义指标迭代函数                                               |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    if(rates_total < 1) return 0;
    
    datetime currentTime = TimeCurrent();
    bool needUpdate = false;
    
    // 检查是否需要更新数据
    if(g_lastUpdateTime == 0 || currentTime - g_lastUpdateTime >= 60) // 每分钟更新一次
    {
        needUpdate = true;
        g_lastUpdateTime = currentTime;
    }
    
    if(!needUpdate) return rates_total;
    
    // 更新数据收集器
    g_dataCollector.UpdateData(rates_total, time, high, low, close);
    
    // 检查是否需要更新宏观数据（每日更新）
    datetime currentDay = iTime(_Symbol, PERIOD_D1, 0);
    if(g_lastDayUpdate != currentDay)
    {
        SMacroData macroData;
        if(g_macroAnalyzer.Calculate(macroData))
        {
            g_panelManager.UpdateMacroData(macroData);
            g_lastDayUpdate = currentDay;
        }
    }
    
    // 检查是否需要更新中观数据（每小时更新）
    datetime currentHour = iTime(_Symbol, PERIOD_H1, 0);
    if(g_lastHourUpdate != currentHour)
    {
        SMesoData mesoData;
        if(g_mesoAnalyzer.Calculate(mesoData))
        {
            g_panelManager.UpdateMesoData(mesoData);
            g_lastHourUpdate = currentHour;
        }
    }
    
    // 更新微观数据（实时更新）
    SMicroData microData;
    if(g_microAnalyzer.Calculate(microData))
    {
        g_panelManager.UpdateMicroData(microData);
    }
    
    // 刷新面板显示
    g_panelManager.RefreshPanel();
    
    return rates_total;
}

//+------------------------------------------------------------------+
//| 自定义指标反初始化函数                                           |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 清理面板
    if(g_panelManager != NULL)
    {
        g_panelManager.Cleanup();
        delete g_panelManager;
        g_panelManager = NULL;
    }
    
    // 清理分析器
    if(g_macroAnalyzer != NULL)
    {
        delete g_macroAnalyzer;
        g_macroAnalyzer = NULL;
    }
    
    if(g_mesoAnalyzer != NULL)
    {
        delete g_mesoAnalyzer;
        g_mesoAnalyzer = NULL;
    }
    
    if(g_microAnalyzer != NULL)
    {
        delete g_microAnalyzer;
        g_microAnalyzer = NULL;
    }
    
    if(g_dataCollector != NULL)
    {
        delete g_dataCollector;
        g_dataCollector = NULL;
    }
    
    Print("专业波动率仪表盘已清理");
}

//+------------------------------------------------------------------+
//| 图表事件处理函数                                                 |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
    if(g_panelManager != NULL)
    {
        g_panelManager.OnChartEvent(id, lparam, dparam, sparam);
    }
}