//+------------------------------------------------------------------+
//|                                               Risk_Manager.mqh |
//|                                          风险管理和资金管理模块  |
//+------------------------------------------------------------------+
#property copyright "Risk Manager Module"
#property version   "1.00"

//+------------------------------------------------------------------+
//| 风险管理器类                                                      |
//+------------------------------------------------------------------+
class CRisk_Manager
{
private:
   //--- 基础参数
   double            m_base_risk_percent;           // 基础风险百分比
   bool              m_use_winning_streak;         // 启用赢冲
   int               m_winning_streak_threshold;   // 赢冲阈值
   double            m_winning_streak_multiplier;  // 赢冲乘数
   bool              m_use_losing_streak;          // 启用输缩
   int               m_losing_streak_threshold;    // 输缩阈值
   double            m_losing_streak_multiplier;   // 输缩乘数
   
   //--- 交易统计
   int               m_consecutive_wins;           // 连续盈利次数
   int               m_consecutive_losses;         // 连续亏损次数
   int               m_total_trades;              // 总交易次数
   int               m_winning_trades;            // 盈利交易次数
   double            m_total_profit;              // 总盈利
   
   //--- 状态标志
   bool              m_initialized;
   long              m_magic_number;              // 魔法数字
   
public:
   //--- 构造函数和析构函数
                     CRisk_Manager();
                    ~CRisk_Manager();
   
   //--- 初始化和清理
   bool              Initialize(double base_risk_percent, bool use_winning_streak, 
                               int winning_streak_threshold, double winning_streak_multiplier,
                               bool use_losing_streak, int losing_streak_threshold, 
                               double losing_streak_multiplier);
   void              Cleanup();
   
   //--- 仓位计算
   double            CalculatePositionSize();
   double            CalculatePositionSize(double stop_loss_distance);
   
   //--- 风险管理
   double            GetCurrentRiskPercent();
   string            GetRiskMode();
   
   //--- 交易统计更新
   void              UpdateTradeStatistics();
   void              OnTradeClose(double profit);
   
   //--- 统计信息获取
   //--- 统计信息获取
   int               GetWinStreak() { return m_consecutive_wins; }
   int               GetLoseStreak() { return m_consecutive_losses; }
   int               GetTotalTrades() { return m_total_trades; }
   double            GetWinRate();
   double            GetTotalProfit() { return m_total_profit; }
   
   //--- 设置方法
   void              SetMagicNumber(long magic_number) { m_magic_number = magic_number; }
   
private:
   //--- 内部方法
   void              ResetStreaks();
   double            GetAccountRisk();
   bool              LoadTradeHistory();
   void              SaveTradeStatistics();
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
CRisk_Manager::CRisk_Manager()
{
   m_initialized = false;
   m_base_risk_percent = 2.0;
   m_use_winning_streak = true;
   m_winning_streak_threshold = 3;
   m_winning_streak_multiplier = 1.5;
   m_use_losing_streak = true;
   m_losing_streak_threshold = 2;
   m_losing_streak_multiplier = 0.5;
   
   m_consecutive_wins = 0;
   m_consecutive_losses = 0;
   m_total_trades = 0;
   m_winning_trades = 0;
   m_total_profit = 0.0;
   m_magic_number = 0;
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
CRisk_Manager::~CRisk_Manager()
{
   Cleanup();
}

//+------------------------------------------------------------------+
//| 初始化风险管理器                                                  |
//+------------------------------------------------------------------+
bool CRisk_Manager::Initialize(double base_risk_percent, bool use_winning_streak, 
                              int winning_streak_threshold, double winning_streak_multiplier,
                              bool use_losing_streak, int losing_streak_threshold, 
                              double losing_streak_multiplier)
{
   Print("初始化风险管理器...");
   
   //--- 保存参数
   m_base_risk_percent = base_risk_percent;
   m_use_winning_streak = use_winning_streak;
   m_winning_streak_threshold = winning_streak_threshold;
   m_winning_streak_multiplier = winning_streak_multiplier;
   m_use_losing_streak = use_losing_streak;
   m_losing_streak_threshold = losing_streak_threshold;
   m_losing_streak_multiplier = losing_streak_multiplier;
   
   //--- 加载历史交易统计
   LoadTradeHistory();
   
   m_initialized = true;
   Print("风险管理器初始化完成");
   Print("基础风险: ", m_base_risk_percent, "%, 连胜: ", m_consecutive_wins, ", 连亏: ", m_consecutive_losses);
   
   return true;
}

//+------------------------------------------------------------------+
//| 清理资源                                                          |
//+------------------------------------------------------------------+
void CRisk_Manager::Cleanup()
{
   if(m_initialized)
   {
      SaveTradeStatistics();
      m_initialized = false;
   }
}

//+------------------------------------------------------------------+
//| 计算仓位大小                                                      |
//+------------------------------------------------------------------+
double CRisk_Manager::CalculatePositionSize()
{
   if(!m_initialized)
      return 0.0;
   
   //--- 获取当前风险百分比
   double current_risk_percent = GetCurrentRiskPercent();
   
   //--- 获取账户净值
   double account_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   if(account_equity <= 0)
   {
      Print("账户净值获取失败");
      return 0.0;
   }
   
   //--- 计算风险金额
   double risk_amount = account_equity * current_risk_percent / 100.0;
   
   //--- 获取ATR值作为止损距离
   int atr_handle = iATR(Symbol(), PERIOD_CURRENT, 14);
   if(atr_handle == INVALID_HANDLE)
   {
      Print("ATR指标创建失败");
      return 0.0;
   }
   
   double atr_buffer[];
   if(CopyBuffer(atr_handle, 0, 0, 1, atr_buffer) != 1)
   {
      Print("ATR数据获取失败");
      IndicatorRelease(atr_handle);
      return 0.0;
   }
   
   double atr_value = atr_buffer[0];
   IndicatorRelease(atr_handle);
   
   //--- 计算止损距离（ATR * 1.5）
   double stop_loss_distance = atr_value * 1.5;
   
   return CalculatePositionSize(stop_loss_distance);
}

//+------------------------------------------------------------------+
//| 根据止损距离计算仓位大小                                          |
//+------------------------------------------------------------------+
double CRisk_Manager::CalculatePositionSize(double stop_loss_distance)
{
   if(!m_initialized || stop_loss_distance <= 0)
      return 0.0;
   
   //--- 获取当前风险百分比
   double current_risk_percent = GetCurrentRiskPercent();
   
   //--- 获取账户净值
   double account_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   if(account_equity <= 0)
      return 0.0;
   
   //--- 计算风险金额
   double risk_amount = account_equity * current_risk_percent / 100.0;
   
   //--- 获取合约规格
   double contract_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_CONTRACT_SIZE);
   double tick_value = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
   
   if(contract_size <= 0 || tick_value <= 0 || tick_size <= 0)
   {
      Print("合约规格获取失败");
      return 0.0;
   }
   
   //--- 计算每点价值
   double point_value = tick_value * SymbolInfoDouble(Symbol(), SYMBOL_POINT) / tick_size;
   
   //--- 计算止损点数
   double stop_loss_points = stop_loss_distance / SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   
   //--- 计算仓位大小
   double position_size = risk_amount / (stop_loss_points * point_value);
   
   //--- 标准化仓位大小
   double min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);
   
   //--- 调整到最小步长
   position_size = MathFloor(position_size / lot_step) * lot_step;
   
   //--- 限制在允许范围内
   position_size = MathMax(position_size, min_lot);
   position_size = MathMin(position_size, max_lot);
   
   Print("仓位计算: 风险=", current_risk_percent, "%, 风险金额=", risk_amount, 
         ", 止损距离=", stop_loss_distance, ", 仓位=", position_size);
   
   return position_size;
}

//+------------------------------------------------------------------+
//| 获取当前风险百分比                                                |
//+------------------------------------------------------------------+
double CRisk_Manager::GetCurrentRiskPercent()
{
   if(!m_initialized)
      return m_base_risk_percent;
   
   //--- 检查赢冲条件
   if(m_use_winning_streak && m_consecutive_wins >= m_winning_streak_threshold)
   {
      return m_base_risk_percent * m_winning_streak_multiplier;
   }
   
   //--- 检查输缩条件
   if(m_use_losing_streak && m_consecutive_losses >= m_losing_streak_threshold)
   {
      return m_base_risk_percent * m_losing_streak_multiplier;
   }
   
   //--- 返回基础风险
   return m_base_risk_percent;
}

//+------------------------------------------------------------------+
//| 获取风险模式描述                                                  |
//+------------------------------------------------------------------+
string CRisk_Manager::GetRiskMode()
{
   if(!m_initialized)
      return "未初始化";
   
   if(m_use_winning_streak && m_consecutive_wins >= m_winning_streak_threshold)
   {
      return "赢冲模式";
   }
   
   if(m_use_losing_streak && m_consecutive_losses >= m_losing_streak_threshold)
   {
      return "输缩模式";
   }
   
   return "常规模式";
}

//+------------------------------------------------------------------+
//| 更新交易统计                                                      |
//+------------------------------------------------------------------+
void CRisk_Manager::UpdateTradeStatistics()
{
   if(!m_initialized)
      return;
   
   //--- 检查是否有新的已平仓交易
   HistorySelect(0, TimeCurrent());
   int total_deals = HistoryDealsTotal();
   
   static int last_deal_count = 0;
   if(total_deals <= last_deal_count)
      return;
   
   //--- 处理新的交易
   for(int i = last_deal_count; i < total_deals; i++)
   {
      ulong deal_ticket = HistoryDealGetTicket(i);
      if(deal_ticket == 0)
         continue;
      
      //--- 检查是否是我们的交易
      long deal_magic = HistoryDealGetInteger(deal_ticket, DEAL_MAGIC);
      if(deal_magic != m_magic_number)
         continue;
      
      //--- 检查是否是平仓交易
      ENUM_DEAL_TYPE deal_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(deal_ticket, DEAL_TYPE);
      if(deal_type != DEAL_TYPE_BUY && deal_type != DEAL_TYPE_SELL)
         continue;
      
      //--- 获取交易盈亏
      double deal_profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
      double deal_swap = HistoryDealGetDouble(deal_ticket, DEAL_SWAP);
      double deal_commission = HistoryDealGetDouble(deal_ticket, DEAL_COMMISSION);
      
      double total_result = deal_profit + deal_swap + deal_commission;
      
      //--- 更新统计
      OnTradeClose(total_result);
   }
   
   last_deal_count = total_deals;
}

//+------------------------------------------------------------------+
//| 处理交易平仓                                                      |
//+------------------------------------------------------------------+
void CRisk_Manager::OnTradeClose(double profit)
{
   if(!m_initialized)
      return;
   
   m_total_trades++;
   m_total_profit += profit;
   
   if(profit > 0)
   {
      //--- 盈利交易
      m_winning_trades++;
      m_consecutive_wins++;
      m_consecutive_losses = 0; // 重置连亏
      
      Print("盈利交易: +", profit, ", 连胜: ", m_consecutive_wins);
   }
   else
   {
      //--- 亏损交易
      m_consecutive_losses++;
      m_consecutive_wins = 0; // 重置连胜
      
      Print("亏损交易: ", profit, ", 连亏: ", m_consecutive_losses);
   }
   
   //--- 保存统计数据
   SaveTradeStatistics();
}

//+------------------------------------------------------------------+
//| 获取胜率                                                          |
//+------------------------------------------------------------------+
double CRisk_Manager::GetWinRate()
{
   if(m_total_trades == 0)
      return 0.0;
   
   return (double)m_winning_trades / m_total_trades * 100.0;
}

//+------------------------------------------------------------------+
//| 加载交易历史统计                                                  |
//+------------------------------------------------------------------+
bool CRisk_Manager::LoadTradeHistory()
{
   //--- 从全局变量加载统计数据
   string prefix = "GMMA_Risk_";
   
   if(GlobalVariableCheck(prefix + "ConsecutiveWins"))
      m_consecutive_wins = (int)GlobalVariableGet(prefix + "ConsecutiveWins");
   
   if(GlobalVariableCheck(prefix + "ConsecutiveLosses"))
      m_consecutive_losses = (int)GlobalVariableGet(prefix + "ConsecutiveLosses");
   
   if(GlobalVariableCheck(prefix + "TotalTrades"))
      m_total_trades = (int)GlobalVariableGet(prefix + "TotalTrades");
   
   if(GlobalVariableCheck(prefix + "WinningTrades"))
      m_winning_trades = (int)GlobalVariableGet(prefix + "WinningTrades");
   
   if(GlobalVariableCheck(prefix + "TotalProfit"))
      m_total_profit = GlobalVariableGet(prefix + "TotalProfit");
   
   return true;
}

//+------------------------------------------------------------------+
//| 保存交易统计                                                      |
//+------------------------------------------------------------------+
void CRisk_Manager::SaveTradeStatistics()
{
   //--- 保存到全局变量
   string prefix = "GMMA_Risk_";
   
   GlobalVariableSet(prefix + "ConsecutiveWins", m_consecutive_wins);
   GlobalVariableSet(prefix + "ConsecutiveLosses", m_consecutive_losses);
   GlobalVariableSet(prefix + "TotalTrades", m_total_trades);
   GlobalVariableSet(prefix + "WinningTrades", m_winning_trades);
   GlobalVariableSet(prefix + "TotalProfit", m_total_profit);
}