//+------------------------------------------------------------------+
//|                                              GMMA_Indicator.mqh |
//|                                    GMMA指标计算和管理模块        |
//+------------------------------------------------------------------+
#property copyright "GMMA Indicator Module"
#property version   "1.00"

//+------------------------------------------------------------------+
//| GMMA指标类                                                        |
//+------------------------------------------------------------------+
class CGMMA_Indicator
{
private:
   //--- 指标句柄数组
   int               m_short_handles[6];    // 短期组EMA句柄
   int               m_long_handles[6];     // 长期组EMA句柄
   
   //--- 参数
   int               m_short_periods[6];    // 短期组周期
   int               m_long_periods[6];     // 长期组周期
   ENUM_MA_METHOD    m_ma_method;          // 移动平均方法
   ENUM_APPLIED_PRICE m_applied_price;     // 应用价格
   
   //--- 数据缓存 - 修复数组定义
   double            m_short_values[6][3];  // 短期组数值[指标][时间]
   double            m_long_values[6][3];   // 长期组数值[指标][时间]
   
   //--- 状态标志
   bool              m_initialized;         // 初始化标志
   int               m_short_count;         // 短期组数量
   int               m_long_count;          // 长期组数量
   
public:
   //--- 构造函数和析构函数
                     CGMMA_Indicator();
                    ~CGMMA_Indicator();
   
   //--- 初始化和清理
   bool              Initialize(string short_periods_str, string long_periods_str, 
                               ENUM_MA_METHOD method, ENUM_APPLIED_PRICE applied_price);
   void              Cleanup();
   
   //--- 数据更新
   bool              Update();
   
   //--- 数据获取
   bool              GetShortGroupValues(double &values[], int shift = 0);
   bool              GetLongGroupValues(double &values[], int shift = 0);
   bool              GetAllValues(double &short_values[], double &long_values[], int shift = 0);
   
   //--- 状态检查
   bool              IsInitialized() { return m_initialized; }
   int               GetShortGroupCount() { return m_short_count; }
   int               GetLongGroupCount() { return m_long_count; }
   
private:
   //--- 内部方法
   bool              ParsePeriods(string periods_str, int &periods[], int &count);
   bool              CreateIndicatorHandles();
   void              ReleaseHandles();
   bool              UpdateIndicatorData();
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
CGMMA_Indicator::CGMMA_Indicator()
{
   m_initialized = false;
   m_ma_method = MODE_EMA;
   m_applied_price = PRICE_CLOSE;
   m_short_count = 0;
   m_long_count = 0;
   
   // 初始化句柄为无效值
   for(int i = 0; i < 6; i++)
   {
      m_short_handles[i] = INVALID_HANDLE;
      m_long_handles[i] = INVALID_HANDLE;
   }
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
CGMMA_Indicator::~CGMMA_Indicator()
{
   Cleanup();
}

//+------------------------------------------------------------------+
//| 初始化GMMA指标                                                    |
//+------------------------------------------------------------------+
bool CGMMA_Indicator::Initialize(string short_periods_str, string long_periods_str, 
                                ENUM_MA_METHOD method, ENUM_APPLIED_PRICE applied_price)
{
   Print("初始化GMMA指标...");
   
   //--- 保存参数
   m_ma_method = method;
   m_applied_price = applied_price;
   
   //--- 解析周期字符串
   if(!ParsePeriods(short_periods_str, m_short_periods, m_short_count))
   {
      Print("短期组周期解析失败: ", short_periods_str);
      return false;
   }
   
   if(!ParsePeriods(long_periods_str, m_long_periods, m_long_count))
   {
      Print("长期组周期解析失败: ", long_periods_str);
      return false;
   }
   
   //--- 验证周期数量
   if(m_short_count == 0 || m_long_count == 0)
   {
      Print("GMMA周期配置错误");
      return false;
   }
   
   //--- 创建指标句柄
   if(!CreateIndicatorHandles())
   {
      Print("创建指标句柄失败");
      return false;
   }
   
   m_initialized = true;
   Print("GMMA指标初始化成功，短期组:", m_short_count, "条，长期组:", m_long_count, "条");
   
   return true;
}

//+------------------------------------------------------------------+
//| 清理资源                                                          |
//+------------------------------------------------------------------+
void CGMMA_Indicator::Cleanup()
{
   if(m_initialized)
   {
      ReleaseHandles();
      m_initialized = false;
      m_short_count = 0;
      m_long_count = 0;
   }
}

//+------------------------------------------------------------------+
//| 解析周期字符串                                                    |
//+------------------------------------------------------------------+
bool CGMMA_Indicator::ParsePeriods(string periods_str, int &periods[], int &count)
{
   string period_strings[];
   int str_count = StringSplit(periods_str, ',', period_strings);
   
   if(str_count <= 0 || str_count > 6)
      return false;
   
   count = 0;
   
   for(int i = 0; i < str_count; i++)
   {
      StringTrimLeft(period_strings[i]);
      StringTrimRight(period_strings[i]);
      
      int period = (int)StringToInteger(period_strings[i]);
      if(period <= 0)
      {
         Print("无效的周期值: ", period_strings[i]);
         return false;
      }
      
      periods[count] = period;
      count++;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 创建指标句柄                                                      |
//+------------------------------------------------------------------+
bool CGMMA_Indicator::CreateIndicatorHandles()
{
   //--- 创建短期组句柄
   for(int i = 0; i < m_short_count; i++)
   {
      m_short_handles[i] = iMA(Symbol(), PERIOD_CURRENT, m_short_periods[i], 0, m_ma_method, m_applied_price);
      if(m_short_handles[i] == INVALID_HANDLE)
      {
         Print("创建短期EMA句柄失败，周期: ", m_short_periods[i]);
         return false;
      }
   }
   
   //--- 创建长期组句柄
   for(int i = 0; i < m_long_count; i++)
   {
      m_long_handles[i] = iMA(Symbol(), PERIOD_CURRENT, m_long_periods[i], 0, m_ma_method, m_applied_price);
      if(m_long_handles[i] == INVALID_HANDLE)
      {
         Print("创建长期EMA句柄失败，周期: ", m_long_periods[i]);
         return false;
      }
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 释放句柄                                                          |
//+------------------------------------------------------------------+
void CGMMA_Indicator::ReleaseHandles()
{
   //--- 释放短期组句柄
   for(int i = 0; i < m_short_count; i++)
   {
      if(m_short_handles[i] != INVALID_HANDLE)
      {
         IndicatorRelease(m_short_handles[i]);
         m_short_handles[i] = INVALID_HANDLE;
      }
   }
   
   //--- 释放长期组句柄
   for(int i = 0; i < m_long_count; i++)
   {
      if(m_long_handles[i] != INVALID_HANDLE)
      {
         IndicatorRelease(m_long_handles[i]);
         m_long_handles[i] = INVALID_HANDLE;
      }
   }
}

//+------------------------------------------------------------------+
//| 更新指标数据                                                      |
//+------------------------------------------------------------------+
bool CGMMA_Indicator::Update()
{
   if(!m_initialized)
      return false;
   
   return UpdateIndicatorData();
}

//+------------------------------------------------------------------+
//| 更新指标数据                                                      |
//+------------------------------------------------------------------+
bool CGMMA_Indicator::UpdateIndicatorData()
{
   //--- 更新短期组数据
   for(int i = 0; i < m_short_count; i++)
   {
      double buffer[3];
      if(CopyBuffer(m_short_handles[i], 0, 0, 3, buffer) != 3)
      {
         Print("获取短期EMA数据失败，周期: ", m_short_periods[i]);
         return false;
      }
      
      for(int j = 0; j < 3; j++)
      {
         m_short_values[i][j] = buffer[2-j]; // 反转数组，[0]为最新值
      }
   }
   
   //--- 更新长期组数据
   for(int i = 0; i < m_long_count; i++)
   {
      double buffer[3];
      if(CopyBuffer(m_long_handles[i], 0, 0, 3, buffer) != 3)
      {
         Print("获取长期EMA数据失败，周期: ", m_long_periods[i]);
         return false;
      }
      
      for(int j = 0; j < 3; j++)
      {
         m_long_values[i][j] = buffer[2-j]; // 反转数组，[0]为最新值
      }
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 获取短期组数值                                                    |
//+------------------------------------------------------------------+
bool CGMMA_Indicator::GetShortGroupValues(double &values[], int shift = 0)
{
   if(!m_initialized || shift < 0 || shift >= 3)
      return false;
   
   ArrayResize(values, m_short_count);
   
   for(int i = 0; i < m_short_count; i++)
   {
      values[i] = m_short_values[i][shift];
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 获取长期组数值                                                    |
//+------------------------------------------------------------------+
bool CGMMA_Indicator::GetLongGroupValues(double &values[], int shift = 0)
{
   if(!m_initialized || shift < 0 || shift >= 3)
      return false;
   
   ArrayResize(values, m_long_count);
   
   for(int i = 0; i < m_long_count; i++)
   {
      values[i] = m_long_values[i][shift];
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 获取所有组数值                                                    |
//+------------------------------------------------------------------+
bool CGMMA_Indicator::GetAllValues(double &short_values[], double &long_values[], int shift = 0)
{
   return GetShortGroupValues(short_values, shift) && GetLongGroupValues(long_values, shift);
}