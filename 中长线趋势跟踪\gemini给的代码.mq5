//+------------------------------------------------------------------+
//|                                       AllWeather_Adaptive_EA.mq5 |
//|          一个结合趋势跟踪与区间策略的24小时全自动交易系统        |
//|                                             Created by Gemini AI |
//+------------------------------------------------------------------+
#property copyright "Gemini AI & User"
#property link      "https://google.com"
#property version   "1.00"
#property description "核心: H1/H4周期判断市场状态(趋势/盘整), M5周期精确执行。趋势市采用多时间框架EMA系统+金字塔加仓; 盘整市采用时段波动率对冲策略。"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

//--- 交易对象
CTrade trade;
CPositionInfo position;
CAccountInfo account;

//+------------------------------------------------------------------+
//| 输入参数 (Inputs)
//+------------------------------------------------------------------+
//--- === 核心设置 ===
input group           "核心设置"
input ulong           MagicNumber = ********;       // EA魔术数字
input double          Initial_Risk_Percent = 1.0;   // 初始仓位风险百分比(%)
input double          Pyramid_Risk_Percent = 0.5;   // 加仓仓位风险百分比(%)
input int             Max_Pyramid_Count = 3;        // 最大加仓次数

//--- === 步骤 1 & 2: 趋势跟踪模块 (H1/M5) ===
input group           "趋势跟踪模块 (步骤 1 & 2)"
input ENUM_TIMEFRAMES Trend_Timeframe = PERIOD_H1;  // 趋势判断时间框架
input int             Trend_Slow_EMA = 55;          // 趋势判断 - 慢速EMA周期
input int             Trend_Fast_EMA = 21;          // 趋势判断 - 快速EMA周期 (用于交叉)
input ENUM_TIMEFRAMES Entry_Timeframe = PERIOD_M5;  // 入场执行时间框架
input int             Entry_EMA = 20;               // 入场执行 - EMA周期
input int             Entry_ATR_Period = 14;        // 入场执行 - ATR周期 (用于止损)
input double          Entry_ATR_Multiplier = 2.0;   // 入场执行 - ATR止损倍数
input bool            Use_Trailing_Stop = true;     // 启用移动止损
input double          Trailing_ATR_Multiplier = 1.5;// 移动止损ATR倍数

//--- === 步骤 3: 盘整区间模块 ===
input group           "盘整区间模块 (步骤 3)"
input int             ADX_Period = 14;              // ADX周期 (判断盘整)
input double          ADX_Range_Threshold = 20.0;   // ADX盘整阈值 (低于此值为盘整)
input group           "--- 亚洲时段 ---"
input bool            Enable_Asia_Session = true;   // 启用亚洲时段策略
input string          Asia_Open_Time = "02:00";     // 亚洲时段开盘时间 (服务器时间)
input group           "--- 欧洲时段 ---"
input bool            Enable_Europe_Session = true; // 启用欧洲时段策略
input string          Europe_Open_Time = "09:00";   // 欧洲时段开盘时间 (服务器时间)
input group           "--- 美洲时段 ---"
input bool            Enable_America_Session = true;// 启用美洲时段策略
input string          America_Open_Time = "15:30";  // 美洲时段开盘时间 (服务器时间)
input int             Range_ATR_Period = 24;        // 区间策略 - ATR参考周期 (H1)
input double          Range_Lot_Size = 0.01;        // 区间策略 - 固定手数

//--- 全局变量
//--- 指标句柄
int h1_slow_ema_handle, h1_fast_ema_handle, h1_adx_handle, h1_atr_handle;
int m5_ema_handle, m5_atr_handle;

//--- 市场状态
enum EnumMarketRegime {
    REGIME_TREND_UP,    // 上升趋势
    REGIME_TREND_DOWN,  // 下降趋势
    REGIME_RANGING,     // 盘整区间
    REGIME_UNDEFINED    // 未定义
};
EnumMarketRegime current_regime = REGIME_UNDEFINED;

datetime last_tick_time = 0;

//+------------------------------------------------------------------+
//| 专家初始化函数
//+------------------------------------------------------------------+
int OnInit() {
    //--- 初始化交易对象
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(10);
    trade.SetTypeFilling(ORDER_FILLING_FOK);

    //--- 初始化指标句柄 (H1)
    h1_slow_ema_handle = iMA(Symbol(), Trend_Timeframe, Trend_Slow_EMA, 0, MODE_EMA, PRICE_CLOSE);
    h1_fast_ema_handle = iMA(Symbol(), Trend_Timeframe, Trend_Fast_EMA, 0, MODE_EMA, PRICE_CLOSE);
    h1_adx_handle = iADX(Symbol(), Trend_Timeframe, ADX_Period);
    h1_atr_handle = iATR(Symbol(), Trend_Timeframe, Range_ATR_Period);

    //--- 初始化指标句柄 (M5)
    m5_ema_handle = iMA(Symbol(), Entry_Timeframe, Entry_EMA, 0, MODE_EMA, PRICE_CLOSE);
    m5_atr_handle = iATR(Symbol(), Entry_Timeframe, Entry_ATR_Period);

    //--- 检查句柄
    if (h1_slow_ema_handle == INVALID_HANDLE || h1_fast_ema_handle == INVALID_HANDLE || h1_adx_handle == INVALID_HANDLE ||
        m5_ema_handle == INVALID_HANDLE || m5_atr_handle == INVALID_HANDLE || h1_atr_handle == INVALID_HANDLE) {
        Print("❌ 错误: 初始化指标失败！");
        return(INIT_FAILED);
    }

    Print("✅ 全天候自适应EA初始化成功。系统准备就绪。");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 专家反初始化函数
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    IndicatorRelease(h1_slow_ema_handle);
    IndicatorRelease(h1_fast_ema_handle);
    IndicatorRelease(h1_adx_handle);
    IndicatorRelease(h1_atr_handle);
    IndicatorRelease(m5_ema_handle);
    IndicatorRelease(m5_atr_handle);
    Print("✅ 全天候自适应EA已停止。");
}

//+------------------------------------------------------------------+
//| 专家Tick函数 (EA的大脑和调度中心)
//+------------------------------------------------------------------+
void OnTick() {
    //--- 简单的Tick节流，防止过于频繁的计算
    if (TimeCurrent() - last_tick_time < 1) {
        return;
    }
    last_tick_time = TimeCurrent();

    //--- 1. 判断当前市场状态 (H1)
    DetermineMarketRegime();

    //--- 2. 策略调度中心
    switch (current_regime) {
        case REGIME_TREND_UP:
        case REGIME_TREND_DOWN:
            // 如果是趋势市，执行趋势跟踪模块
            ExecuteTrendStrategy();
            break;

        case REGIME_RANGING:
            // 如果是盘整市，执行区间对冲模块
            ExecuteRangeStrategy();
            break;

        default:
            // 未定义状态，观望
            break;
    }
    
    //--- 3. 对所有持仓执行移动止损
    if(Use_Trailing_Stop){
        ManageTrailingStops();
    }
}

//+------------------------------------------------------------------+
//| 1. 判断市场状态 (H1)
//+------------------------------------------------------------------+
void DetermineMarketRegime() {
    //--- 获取H1 ADX值
    double adx_buffer[1];
    if (CopyBuffer(h1_adx_handle, 0, 1, 1, adx_buffer) <= 0) return;
    double adx_value = adx_buffer[0];

    //--- ADX低于阈值，定义为盘整市
    if (adx_value < ADX_Range_Threshold) {
        current_regime = REGIME_RANGING;
        return;
    }

    //--- ADX高于阈值，定义为趋势市，再判断方向
    double fast_ema_buffer[2], slow_ema_buffer[2];
    if (CopyBuffer(h1_fast_ema_handle, 0, 1, 2, fast_ema_buffer) <= 0 || CopyBuffer(h1_slow_ema_handle, 0, 1, 2, slow_ema_buffer) <= 0) return;

    //--- 判断金叉/死叉
    bool is_bullish_cross = fast_ema_buffer[0] > slow_ema_buffer[0] && fast_ema_buffer[1] <= slow_ema_buffer[1];
    bool is_bearish_cross = fast_ema_buffer[0] < slow_ema_buffer[0] && fast_ema_buffer[1] >= slow_ema_buffer[1];
    
    //--- 判断当前趋势状态
    if(fast_ema_buffer[0] > slow_ema_buffer[0]){
        current_regime = REGIME_TREND_UP;
    } else {
        current_regime = REGIME_TREND_DOWN;
    }
}

//+------------------------------------------------------------------+
//| 2.A 执行趋势跟踪策略 (M5)
//+------------------------------------------------------------------+
void ExecuteTrendStrategy() {
    //--- 确保没有区间策略的仓位在场
    ClosePositionsByComment("Range_Strategy");

    //--- 获取M5指标数据
    double m5_ema_buffer[2], m5_atr_buffer[1];
    if (CopyBuffer(m5_ema_handle, 0, 1, 2, m5_ema_buffer) <= 0 || CopyBuffer(m5_atr_handle, 0, 1, 1, m5_atr_buffer) <= 0) return;
    
    double m5_close = iClose(Symbol(), Entry_Timeframe, 1);
    double m5_prev_close = iClose(Symbol(), Entry_Timeframe, 2);
    double m5_atr = m5_atr_buffer[0];

    int total_positions = PositionsTotal();
    int pyramid_count = CountPositionsByComment("Pyramid");

    //--- 初始入场逻辑
    if (total_positions == 0) {
        // 多头趋势下的买入信号: M5价格上穿EMA
        if (current_regime == REGIME_TREND_UP && m5_close > m5_ema_buffer[0] && m5_prev_close <= m5_ema_buffer[0]) {
            double sl_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK) - m5_atr * Entry_ATR_Multiplier;
            double lots = CalculateRiskLots(sl_price, ORDER_TYPE_BUY);
            trade.Buy(lots, Symbol(), 0, sl_price, 0, "Trend_Initial");
        }
        // 空头趋势下的卖出信号: M5价格下穿EMA
        if (current_regime == REGIME_TREND_DOWN && m5_close < m5_ema_buffer[0] && m5_prev_close >= m5_ema_buffer[0]) {
            double sl_price = SymbolInfoDouble(Symbol(), SYMBOL_BID) + m5_atr * Entry_ATR_Multiplier;
            double lots = CalculateRiskLots(sl_price, ORDER_TYPE_SELL);
            trade.Sell(lots, Symbol(), 0, sl_price, 0, "Trend_Initial");
        }
    }
    //--- 金字塔加仓逻辑
    else if (total_positions > 0 && pyramid_count < Max_Pyramid_Count) {
        // 多头趋势下的加仓信号: 回调至M5 EMA获得支撑
        if (current_regime == REGIME_TREND_UP && IsPullbackAndGo(ORDER_TYPE_BUY, m5_ema_buffer[0])) {
            double sl_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK) - m5_atr * Entry_ATR_Multiplier;
            double lots = CalculateRiskLots(sl_price, ORDER_TYPE_BUY, true);
            trade.Buy(lots, Symbol(), 0, sl_price, 0, "Pyramid");
        }
        // 空头趋势下的加仓信号: 反弹至M5 EMA遭遇阻力
        if (current_regime == REGIME_TREND_DOWN && IsPullbackAndGo(ORDER_TYPE_SELL, m5_ema_buffer[0])) {
            double sl_price = SymbolInfoDouble(Symbol(), SYMBOL_BID) + m5_atr * Entry_ATR_Multiplier;
            double lots = CalculateRiskLots(sl_price, ORDER_TYPE_SELL, true);
            trade.Sell(lots, Symbol(), 0, sl_price, 0, "Pyramid");
        }
    }
    
    //--- 趋势反转平仓逻辑
    if(total_positions > 0){
        bool holding_long = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY);
        bool holding_short = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL);

        if(holding_long && current_regime == REGIME_TREND_DOWN){
            trade.PositionClose(Symbol());
        }
        if(holding_short && current_regime == REGIME_TREND_UP){
            trade.PositionClose(Symbol());
        }
    }
}

//+------------------------------------------------------------------+
//| 2.B 执行区间对冲策略
//+------------------------------------------------------------------+
void ExecuteRangeStrategy() {
    //--- 确保没有趋势策略的仓位在场
    ClosePositionsByComment("Trend_Initial");
    ClosePositionsByComment("Pyramid");

    //--- 检查是否到达交易时段
    string current_time_str = TimeToString(TimeCurrent(), TIME_MINUTES);
    bool is_trade_time = false;
    if (Enable_Asia_Session && StringFind(current_time_str, Asia_Open_Time) >= 0) is_trade_time = true;
    if (Enable_Europe_Session && StringFind(current_time_str, Europe_Open_Time) >= 0) is_trade_time = true;
    if (Enable_America_Session && StringFind(current_time_str, America_Open_Time) >= 0) is_trade_time = true;

    if (is_trade_time && PositionsTotal() == 0) {
        //--- 获取H1 ATR作为波动率参考
        double h1_atr_buffer[1];
        if (CopyBuffer(h1_atr_handle, 0, 1, 1, h1_atr_buffer) <= 0) return;
        double volatility_range = h1_atr_buffer[0];

        //--- 开设对锁仓位
        double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
        double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
        
        //--- 开多单
        double buy_sl = bid - volatility_range;
        double buy_tp = ask + volatility_range;
        trade.Buy(Range_Lot_Size, Symbol(), ask, buy_sl, buy_tp, "Range_Strategy_Buy");
        
        //--- 开空单
        double sell_sl = ask + volatility_range;
        double sell_tp = bid - volatility_range;
        trade.Sell(Range_Lot_Size, Symbol(), bid, sell_sl, sell_tp, "Range_Strategy_Sell");
        
        Print("盘整市: 在 ", current_time_str, " 开启波动率对冲仓位。波动范围: ", DoubleToString(volatility_range, _Digits));
    }
}

//+------------------------------------------------------------------+
//| 辅助函数
//+------------------------------------------------------------------+
//--- 计算基于风险的手数
double CalculateRiskLots(double sl_price, ENUM_ORDER_TYPE direction, bool is_pyramid = false) {
    double risk_percent = is_pyramid ? Pyramid_Risk_Percent : Initial_Risk_Percent;
    double balance = account.Balance();
    double risk_amount = balance * risk_percent / 100.0;
    
    double entry_price = (direction == ORDER_TYPE_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double risk_per_lot = 0;
    if (direction == ORDER_TYPE_BUY) {
        risk_per_lot = (entry_price - sl_price) * SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE) / SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    } else {
        risk_per_lot = (sl_price - entry_price) * SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE) / SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
    }

    if (risk_per_lot <= 0) return 0.01;
    
    double lots = risk_amount / risk_per_lot;
    
    //--- 标准化手数并返回
    return NormalizeDouble(lots, 2);
}

//--- 判断是否为回调买入/反弹卖出
bool IsPullbackAndGo(ENUM_ORDER_TYPE direction, double ema_value) {
    double low_1 = iLow(Symbol(), Entry_Timeframe, 1);
    double high_1 = iHigh(Symbol(), Entry_Timeframe, 1);
    double close_1 = iClose(Symbol(), Entry_Timeframe, 1);
    double open_1 = iOpen(Symbol(), Entry_Timeframe, 1);
    
    if (direction == ORDER_TYPE_BUY) {
        // K线曾触及或跌破EMA，但最终收盘为阳线且在EMA之上
        bool touched_ema = low_1 <= ema_value;
        bool closed_above = close_1 > ema_value;
        bool is_bullish = close_1 > open_1;
        return touched_ema && closed_above && is_bullish;
    } else {
        // K线曾触及或突破EMA，但最终收盘为阴线且在EMA之下
        bool touched_ema = high_1 >= ema_value;
        bool closed_below = close_1 < ema_value;
        bool is_bearish = close_1 < open_1;
        return touched_ema && closed_below && is_bearish;
    }
}

//--- 移动止损管理
void ManageTrailingStops() {
    double m5_atr_buffer[1];
    if (CopyBuffer(m5_atr_handle, 0, 1, 1, m5_atr_buffer) <= 0) return;
    double trailing_distance = m5_atr_buffer[0] * Trailing_ATR_Multiplier;

    for (int i = PositionsTotal() - 1; i >= 0; i--) {
        if (position.SelectByIndex(i) && position.Symbol() == Symbol() && position.Magic() == MagicNumber) {
            double current_sl = position.StopLoss();
            double open_price = position.PriceOpen();
            double new_sl = 0;

            if (position.PositionType() == POSITION_TYPE_BUY) {
                double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
                new_sl = current_price - trailing_distance;
                // 确保止损只向前移动，且超过开仓价
                if (new_sl > current_sl && new_sl > open_price) {
                    trade.PositionModify(position.Ticket(), new_sl, position.TakeProfit());
                }
            } else {
                double current_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
                new_sl = current_price + trailing_distance;
                // 确保止损只向前移动，且超过开仓价
                if (new_sl < current_sl && new_sl < open_price) {
                    trade.PositionModify(position.Ticket(), new_sl, position.TakeProfit());
                }
            }
        }
    }
}

//--- 根据注释平仓
void ClosePositionsByComment(string comment_to_find){
    for (int i = PositionsTotal() - 1; i >= 0; i--) {
        if (position.SelectByIndex(i) && position.Symbol() == Symbol() && position.Magic() == MagicNumber) {
            if(StringFind(position.Comment(), comment_to_find) >= 0){
                trade.PositionClose(position.Ticket());
            }
        }
    }
}

//--- 根据注释计算持仓数
int CountPositionsByComment(string comment_to_find){
    int count = 0;
    for (int i = PositionsTotal() - 1; i >= 0; i--) {
        if (position.SelectByIndex(i) && position.Symbol() == Symbol() && position.Magic() == MagicNumber) {
            if(StringFind(position.Comment(), comment_to_find) >= 0){
                count++;
            }
        }
    }
    return count;
}
